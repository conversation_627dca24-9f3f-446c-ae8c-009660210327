from datetime import datetime
from app import db
import uuid

class AuditLog(db.Model):
    __tablename__ = 'audit_logs'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # User and action details
    user_id = db.Column(db.String(36), db.<PERSON><PERSON>('users.id'))
    action = db.Column(db.String(100), nullable=False)  # e.g., 'create_migration', 'delete_server'
    resource_type = db.Column(db.String(50), nullable=False)  # e.g., 'migration', 'server', 'user'
    resource_id = db.Column(db.String(36))  # ID of the affected resource
    
    # Request details
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    request_method = db.Column(db.String(10))
    request_path = db.Column(db.String(500))
    
    # Action details
    description = db.Column(db.Text)
    old_values = db.Column(db.JSON)  # Previous state (for updates)
    new_values = db.Column(db.JSON)  # New state (for creates/updates)
    
    # Status and result
    status = db.Column(db.String(20), default='success')  # success, failed, error
    error_message = db.Column(db.Text)
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', back_populates='audit_logs')
    
    def __init__(self, action, resource_type, user_id=None, **kwargs):
        self.action = action
        self.resource_type = resource_type
        self.user_id = user_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    @classmethod
    def log_action(cls, action, resource_type, user_id=None, resource_id=None, 
                   description=None, old_values=None, new_values=None, 
                   ip_address=None, user_agent=None, request_method=None, 
                   request_path=None, status='success', error_message=None):
        """Create and save an audit log entry"""
        log_entry = cls(
            action=action,
            resource_type=resource_type,
            user_id=user_id,
            resource_id=resource_id,
            description=description,
            old_values=old_values,
            new_values=new_values,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request_method,
            request_path=request_path,
            status=status,
            error_message=error_message
        )
        
        db.session.add(log_entry)
        db.session.commit()
        return log_entry
    
    def to_dict(self):
        """Convert audit log to dictionary"""
        return {
            'id': self.id,
            'user': self.user.get_full_name() if self.user else 'System',
            'user_id': self.user_id,
            'action': self.action,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'description': self.description,
            'old_values': self.old_values,
            'new_values': self.new_values,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'request_method': self.request_method,
            'request_path': self.request_path,
            'status': self.status,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<AuditLog {self.action} on {self.resource_type}>'
