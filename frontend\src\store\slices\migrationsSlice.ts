import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Migration, MigrationFormData, MigrationTemplate, PaginatedResponse } from '@/types';
import apiService from '@/services/api';

interface MigrationsState {
  migrations: Migration[];
  currentMigration: Migration | null;
  templates: MigrationTemplate[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    pages: number;
    per_page: number;
    total: number;
    has_next: boolean;
    has_prev: boolean;
  } | null;
}

const initialState: MigrationsState = {
  migrations: [],
  currentMigration: null,
  templates: [],
  isLoading: false,
  error: null,
  pagination: null,
};

// Async thunks
export const fetchMigrations = createAsyncThunk(
  'migrations/fetchMigrations',
  async (params?: {
    page?: number;
    per_page?: number;
    search?: string;
    status?: string;
    source_server?: string;
    target_server?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await apiService.getMigrations(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch migrations');
    }
  }
);

export const fetchMigration = createAsyncThunk(
  'migrations/fetchMigration',
  async (id: string, { rejectWithValue }) => {
    try {
      const migration = await apiService.getMigration(id);
      return migration;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch migration');
    }
  }
);

export const createMigration = createAsyncThunk(
  'migrations/createMigration',
  async (data: MigrationFormData, { rejectWithValue }) => {
    try {
      const migration = await apiService.createMigration(data);
      return migration;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create migration');
    }
  }
);

export const updateMigration = createAsyncThunk(
  'migrations/updateMigration',
  async ({ id, data }: { id: string; data: Partial<MigrationFormData> }, { rejectWithValue }) => {
    try {
      const migration = await apiService.updateMigration(id, data);
      return migration;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update migration');
    }
  }
);

export const deleteMigration = createAsyncThunk(
  'migrations/deleteMigration',
  async (id: string, { rejectWithValue }) => {
    try {
      await apiService.deleteMigration(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete migration');
    }
  }
);

export const startMigration = createAsyncThunk(
  'migrations/startMigration',
  async (id: string, { rejectWithValue }) => {
    try {
      await apiService.startMigration(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to start migration');
    }
  }
);

export const pauseMigration = createAsyncThunk(
  'migrations/pauseMigration',
  async (id: string, { rejectWithValue }) => {
    try {
      await apiService.pauseMigration(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to pause migration');
    }
  }
);

export const cancelMigration = createAsyncThunk(
  'migrations/cancelMigration',
  async (id: string, { rejectWithValue }) => {
    try {
      await apiService.cancelMigration(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to cancel migration');
    }
  }
);

export const fetchMigrationTemplates = createAsyncThunk(
  'migrations/fetchTemplates',
  async (_, { rejectWithValue }) => {
    try {
      const templates = await apiService.getMigrationTemplates();
      return templates;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch templates');
    }
  }
);

const migrationsSlice = createSlice({
  name: 'migrations',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentMigration: (state) => {
      state.currentMigration = null;
    },
    updateMigrationStatus: (state, action: PayloadAction<{
      id: string;
      status: string;
      progress?: number;
      step?: string;
    }>) => {
      const { id, status, progress, step } = action.payload;
      
      // Update in migrations list
      const migration = state.migrations.find(m => m.id === id);
      if (migration) {
        migration.status = status as any;
        if (progress !== undefined) migration.progress = progress;
        if (step !== undefined) migration.current_step = step;
      }
      
      // Update current migration
      if (state.currentMigration?.id === id) {
        state.currentMigration.status = status as any;
        if (progress !== undefined) state.currentMigration.progress = progress;
        if (step !== undefined) state.currentMigration.current_step = step;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch migrations
      .addCase(fetchMigrations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMigrations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.migrations = action.payload.items;
        state.pagination = action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchMigrations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch migration
      .addCase(fetchMigration.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMigration.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentMigration = action.payload;
        state.error = null;
      })
      .addCase(fetchMigration.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Create migration
      .addCase(createMigration.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createMigration.fulfilled, (state, action) => {
        state.isLoading = false;
        state.migrations.unshift(action.payload);
        state.error = null;
      })
      .addCase(createMigration.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Update migration
      .addCase(updateMigration.fulfilled, (state, action) => {
        const index = state.migrations.findIndex(m => m.id === action.payload.id);
        if (index !== -1) {
          state.migrations[index] = action.payload;
        }
        if (state.currentMigration?.id === action.payload.id) {
          state.currentMigration = action.payload;
        }
      })
      
      // Delete migration
      .addCase(deleteMigration.fulfilled, (state, action) => {
        state.migrations = state.migrations.filter(m => m.id !== action.payload);
        if (state.currentMigration?.id === action.payload) {
          state.currentMigration = null;
        }
      })
      
      // Start migration
      .addCase(startMigration.fulfilled, (state, action) => {
        const migration = state.migrations.find(m => m.id === action.payload);
        if (migration) {
          migration.status = 'running';
        }
        if (state.currentMigration?.id === action.payload) {
          state.currentMigration.status = 'running';
        }
      })
      
      // Pause migration
      .addCase(pauseMigration.fulfilled, (state, action) => {
        const migration = state.migrations.find(m => m.id === action.payload);
        if (migration) {
          migration.status = 'paused';
        }
        if (state.currentMigration?.id === action.payload) {
          state.currentMigration.status = 'paused';
        }
      })
      
      // Cancel migration
      .addCase(cancelMigration.fulfilled, (state, action) => {
        const migration = state.migrations.find(m => m.id === action.payload);
        if (migration) {
          migration.status = 'cancelled';
        }
        if (state.currentMigration?.id === action.payload) {
          state.currentMigration.status = 'cancelled';
        }
      })
      
      // Fetch templates
      .addCase(fetchMigrationTemplates.fulfilled, (state, action) => {
        state.templates = action.payload;
      });
  },
});

export const { clearError, clearCurrentMigration, updateMigrationStatus } = migrationsSlice.actions;
export default migrationsSlice.reducer;
