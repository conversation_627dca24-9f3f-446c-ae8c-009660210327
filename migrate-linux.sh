#!/bin/bash
# Advanced Linux System Migration Tool

CONFIG_FILE="${1:-migration_config.yaml}"
LOG_FILE="migration.log"
TEMP_DIR="/tmp/linux_migration"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Load YAML parser
source "$SCRIPT_DIR/yaml_parser.sh"

function setup_logging() {
    mkdir -p $(dirname $LOG_FILE)
    exec > >(tee -a $LOG_FILE) 2>&1
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Migration started"
}

function detect_distro() {
    local server=$1
    echo "Detecting Linux distribution on $server..."
    ssh -i $SSH_KEY root@$server "cat /etc/os-release" > $TEMP_DIR/os_info.txt
    DISTRO=$(grep "^ID=" $TEMP_DIR/os_info.txt | cut -d= -f2 | tr -d '"')
    VERSION=$(grep "^VERSION_ID=" $TEMP_DIR/os_info.txt | cut -d= -f2 | tr -d '"')
    echo "Detected: $DISTRO $VERSION"
}

function prepare_source() {
    echo "Preparing source system..."
    ssh -i $SSH_KEY root@$SOURCE_SERVER "apt-get update && apt-get install -y rsync tar gzip"
    
    # Stop non-essential services
    if [[ "$STOP_SERVICES" == "true" ]]; then
        echo "Stopping services on source..."
        ssh -i $SSH_KEY root@$SOURCE_SERVER "systemctl stop apache2 mysql nginx postfix"
    fi
}

function prepare_target() {
    echo "Preparing target system..."
    ssh -i $SSH_KEY root@$TARGET_SERVER "apt-get update && apt-get install -y rsync tar gzip gdisk parted"
    
    # Prepare disk if needed
    if [[ "$PREPARE_DISK" == "true" ]]; then
        echo "Preparing disk on target..."
        ssh -i $SSH_KEY root@$TARGET_SERVER "parted -s $TARGET_DISK mklabel gpt && \
            parted -s $TARGET_DISK mkpart primary ext4 1MiB 512MiB && \
            parted -s $TARGET_DISK mkpart primary ext4 512MiB 100% && \
            mkfs.ext4 ${TARGET_DISK}1 && \
            mkfs.ext4 ${TARGET_DISK}2 && \
            mkdir -p /mnt/newroot /mnt/newboot && \
            mount ${TARGET_DISK}2 /mnt/newroot && \
            mount ${TARGET_DISK}1 /mnt/newboot"
    else
        ssh -i $SSH_KEY root@$TARGET_SERVER "mkdir -p /mnt/newroot"
    fi
}

function create_system_archive() {
    echo "Creating system archive..."
    EXCLUDE_DIRS="/proc/* /sys/* /tmp/* /dev/* /run/* /mnt/* /media/* /lost+found/* /var/cache/apt/* /var/lib/apt/lists/*"
    EXCLUDE_OPTS=""
    
    for dir in $EXCLUDE_DIRS; do
        EXCLUDE_OPTS="$EXCLUDE_OPTS --exclude=$dir"
    done
    
    # Add custom excludes from config
    for dir in "${EXCLUDE_CUSTOM[@]}"; do
        EXCLUDE_OPTS="$EXCLUDE_OPTS --exclude=$dir"
    done
    
    if [[ "$MIGRATION_METHOD" == "tar" ]]; then
        ssh -i $SSH_KEY root@$SOURCE_SERVER "mkdir -p $(dirname $ARCHIVE_PATH) && \
            tar -czpf $ARCHIVE_PATH $EXCLUDE_OPTS /"
    elif [[ "$MIGRATION_METHOD" == "rsync" ]]; then
        echo "Using direct rsync method (no archive)"
    fi
}

function transfer_system() {
    echo "Transferring system to target..."
    
    if [[ "$MIGRATION_METHOD" == "tar" ]]; then
        # Transfer the archive to target server
        scp -i $SSH_KEY root@$SOURCE_SERVER:$ARCHIVE_PATH $TEMP_DIR/
        scp -i $SSH_KEY $TEMP_DIR/$(basename $ARCHIVE_PATH) root@$TARGET_SERVER:$TEMP_DIR/
        
        # Extract archive
        ssh -i $SSH_KEY root@$TARGET_SERVER "tar -xzpf $TEMP_DIR/$(basename $ARCHIVE_PATH) -C /mnt/newroot"
    elif [[ "$MIGRATION_METHOD" == "rsync" ]]; then
        # Direct rsync between servers
        EXCLUDE_OPTS=""
        for dir in $EXCLUDE_DIRS; do
            EXCLUDE_OPTS="$EXCLUDE_OPTS --exclude=$dir"
        done
        
        # Add custom excludes from config
        for dir in "${EXCLUDE_CUSTOM[@]}"; do
            EXCLUDE_OPTS="$EXCLUDE_OPTS --exclude=$dir"
        done
        
        ssh -i $SSH_KEY root@$SOURCE_SERVER "rsync -aAXHv $EXCLUDE_OPTS / root@$TARGET_SERVER:/mnt/newroot/"
    fi
}

function configure_bootloader() {
    echo "Configuring bootloader..."
    
    if [[ "$DISTRO" == "ubuntu" || "$DISTRO" == "debian" ]]; then
        ssh -i $SSH_KEY root@$TARGET_SERVER "mount -t proc none /mnt/newroot/proc && \
            mount -t sysfs none /mnt/newroot/sys && \
            mount -o bind /dev /mnt/newroot/dev && \
            chroot /mnt/newroot /bin/bash -c 'update-grub && grub-install $TARGET_DISK'"
    elif [[ "$DISTRO" == "centos" || "$DISTRO" == "rhel" || "$DISTRO" == "fedora" ]]; then
        ssh -i $SSH_KEY root@$TARGET_SERVER "mount -t proc none /mnt/newroot/proc && \
            mount -t sysfs none /mnt/newroot/sys && \
            mount -o bind /dev /mnt/newroot/dev && \
            chroot /mnt/newroot /bin/bash -c 'grub2-mkconfig -o /boot/grub2/grub.cfg && grub2-install $TARGET_DISK'"
    fi
}

function update_network_config() {
    echo "Updating network configuration..."
    
    if [[ "$DISTRO" == "ubuntu" || "$DISTRO" == "debian" ]]; then
        # For systemd-networkd or Netplan
        if ssh -i $SSH_KEY root@$TARGET_SERVER "[ -d /mnt/newroot/etc/netplan ]"; then
            cat > $TEMP_DIR/01-netcfg.yaml << EOF
network:
  version: 2
  renderer: networkd
  ethernets:
    $NETWORK_INTERFACE:
      dhcp4: $NETWORK_DHCP
      addresses: [$NETWORK_IP/$NETWORK_PREFIX]
      gateway4: $NETWORK_GATEWAY
      nameservers:
        addresses: [$NETWORK_DNS]
EOF
            scp -i $SSH_KEY $TEMP_DIR/01-netcfg.yaml root@$TARGET_SERVER:/mnt/newroot/etc/netplan/
        else
            # For older systems with /etc/network/interfaces
            cat > $TEMP_DIR/interfaces << EOF
auto lo
iface lo inet loopback

auto $NETWORK_INTERFACE
iface $NETWORK_INTERFACE inet static
    address $NETWORK_IP
    netmask $NETWORK_NETMASK
    gateway $NETWORK_GATEWAY
    dns-nameservers $NETWORK_DNS
EOF
            scp -i $SSH_KEY $TEMP_DIR/interfaces root@$TARGET_SERVER:/mnt/newroot/etc/network/
        fi
    elif [[ "$DISTRO" == "centos" || "$DISTRO" == "rhel" || "$DISTRO" == "fedora" ]]; then
        # For RHEL-based systems
        cat > $TEMP_DIR/ifcfg-$NETWORK_INTERFACE << EOF
TYPE=Ethernet
BOOTPROTO=static
IPADDR=$NETWORK_IP
PREFIX=$NETWORK_PREFIX
GATEWAY=$NETWORK_GATEWAY
DNS1=$NETWORK_DNS
DEFROUTE=yes
IPV4_FAILURE_FATAL=no
NAME=$NETWORK_INTERFACE
DEVICE=$NETWORK_INTERFACE
ONBOOT=yes
EOF
        scp -i $SSH_KEY $TEMP_DIR/ifcfg-$NETWORK_INTERFACE root@$TARGET_SERVER:/mnt/newroot/etc/sysconfig/network-scripts/
    fi
    
    # Update hostname
    ssh -i $SSH_KEY root@$TARGET_SERVER "echo '$TARGET_HOSTNAME' > /mnt/newroot/etc/hostname"
    
    # Update hosts file
    ssh -i $SSH_KEY root@$TARGET_SERVER "sed -i 's/*********.*/*********\t$TARGET_HOSTNAME/g' /mnt/newroot/etc/hosts"
}

function update_fstab() {
    echo "Updating fstab..."
    
    # Get UUIDs of new partitions
    ROOT_UUID=$(ssh -i $SSH_KEY root@$TARGET_SERVER "blkid -s UUID -o value ${TARGET_DISK}2")
    BOOT_UUID=$(ssh -i $SSH_KEY root@$TARGET_SERVER "blkid -s UUID -o value ${TARGET_DISK}1")
    
    cat > $TEMP_DIR/fstab << EOF
# /etc/fstab: static file system information
UUID=$ROOT_UUID / ext4 errors=remount-ro 0 1
UUID=$BOOT_UUID /boot ext4 defaults 0 2
tmpfs /tmp tmpfs defaults,noatime,mode=1777 0 0
EOF
    scp -i $SSH_KEY $TEMP_DIR/fstab root@$TARGET_SERVER:/mnt/newroot/etc/
}

function update_users() {
    echo "Updating user passwords..."
    
    # Update root password
    ssh -i $SSH_KEY root@$TARGET_SERVER "chroot /mnt/newroot /bin/bash -c 'echo root:$ROOT_PASSWORD | chpasswd'"
    
    # Update additional users if specified
    for user in "${!USER_PASSWORDS[@]}"; do
        password=${USER_PASSWORDS[$user]}
        ssh -i $SSH_KEY root@$TARGET_SERVER "chroot /mnt/newroot /bin/bash -c 'echo $user:$password | chpasswd'"
    done
}

function configure_cloud_init() {
    if [[ "$USE_CLOUD_INIT" == "true" ]]; then
        echo "Configuring cloud-init..."
        
        # Install cloud-init if not present
        ssh -i $SSH_KEY root@$TARGET_SERVER "chroot /mnt/newroot /bin/bash -c 'apt-get update && apt-get install -y cloud-init'"
        
        # Create cloud-init configuration
        cat > $TEMP_DIR/cloud.cfg << EOF
# Cloud-init configuration
datasource_list: [ NoCloud, None ]
datasource:
  NoCloud:
    seedfrom: /var/lib/cloud/seed/nocloud-net

# System configuration
system_info:
  distro: $DISTRO
  default_user:
    name: $DEFAULT_USER
    sudo: ALL=(ALL) NOPASSWD:ALL

# User configuration
users:
  - default
  - name: $DEFAULT_USER
    passwd: $DEFAULT_USER_PASSWORD_HASH
    lock_passwd: false
    sudo: ALL=(ALL) NOPASSWD:ALL
    shell: /bin/bash

# Network configuration handled separately
EOF
        scp -i $SSH_KEY $TEMP_DIR/cloud.cfg root@$TARGET_SERVER:/mnt/newroot/etc/cloud/cloud.cfg
    fi
}

function finalize_migration() {
    echo "Finalizing migration..."
    
    # Unmount filesystems
    ssh -i $SSH_KEY root@$TARGET_SERVER "umount /mnt/newroot/dev && \
        umount /mnt/newroot/proc && \
        umount /mnt/newroot/sys && \
        umount /mnt/newroot"
    
    if [[ "$PREPARE_DISK" == "true" ]]; then
        ssh -i $SSH_KEY root@$TARGET_SERVER "umount /mnt/newboot"
    fi
    
    echo "Migration completed successfully!"
    echo "The system has been migrated to $TARGET_SERVER"
    echo "You can now reboot the target server to boot into the migrated system"
}

function cleanup() {
    echo "Cleaning up temporary files..."
    rm -rf $TEMP_DIR
    ssh -i $SSH_KEY root@$SOURCE_SERVER "rm -f $ARCHIVE_PATH"
    ssh -i $SSH_KEY root@$TARGET_SERVER "rm -rf $TEMP_DIR"
}

# Main execution
setup_logging
mkdir -p $TEMP_DIR

# Parse config
parse_yaml $CONFIG_FILE
SOURCE_SERVER=$(get_yaml_value "source_server")
TARGET_SERVER=$(get_yaml_value "target_server")
SSH_KEY=$(get_yaml_value "ssh_key")
MIGRATION_METHOD=$(get_yaml_value "migration_method" "tar")
ARCHIVE_PATH=$(get_yaml_value "archive_path" "/tmp/system_backup.tar.gz")
STOP_SERVICES=$(get_yaml_value "stop_services" "true")
PREPARE_DISK=$(get_yaml_value "prepare_disk" "false")
TARGET_DISK=$(get_yaml_value "target_disk" "/dev/sda")
TARGET_HOSTNAME=$(get_yaml_value "target_hostname")
ROOT_PASSWORD=$(get_yaml_value "root_password")
NETWORK_INTERFACE=$(get_yaml_value "network.interface" "eth0")
NETWORK_DHCP=$(get_yaml_value "network.dhcp" "false")
NETWORK_IP=$(get_yaml_value "network.ip")
NETWORK_PREFIX=$(get_yaml_value "network.prefix" "24")
NETWORK_NETMASK=$(get_yaml_value "network.netmask" "*************")
NETWORK_GATEWAY=$(get_yaml_value "network.gateway")
NETWORK_DNS=$(get_yaml_value "network.dns" "*******")
USE_CLOUD_INIT=$(get_yaml_value "use_cloud_init" "false")
DEFAULT_USER=$(get_yaml_value "default_user" "admin")
DEFAULT_USER_PASSWORD_HASH=$(get_yaml_value "default_user_password_hash")
EXCLUDE_CUSTOM=($(get_yaml_array "exclude_dirs"))
USER_PASSWORDS=$(get_yaml_map "user_passwords")

# Execute migration steps
detect_distro $SOURCE_SERVER
prepare_source
prepare_target
create_system_archive
transfer_system
update_fstab
update_network_config
update_users
configure_bootloader
configure_cloud_init
finalize_migration
cleanup