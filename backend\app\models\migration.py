from datetime import datetime
from app import db
import uuid
import json

class Migration(db.Model):
    __tablename__ = 'migrations'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # Source and target servers
    source_server_id = db.Column(db.String(36), db.<PERSON>ey('servers.id'), nullable=False)
    target_server_id = db.Column(db.String(36), db.ForeignKey('servers.id'), nullable=False)
    
    # Migration configuration
    config = db.Column(db.JSON, nullable=False)  # Migration parameters
    template_id = db.Column(db.String(36), db.<PERSON><PERSON>('migration_templates.id'))
    
    # Status and progress
    status = db.Column(db.String(20), default='pending', nullable=False)
    # Status values: pending, running, completed, failed, cancelled, paused
    progress = db.Column(db.Integer, default=0)  # 0-100
    current_step = db.Column(db.String(100))
    error_message = db.Column(db.Text)
    
    # Timing
    scheduled_at = db.Column(db.DateTime)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    estimated_duration = db.Column(db.Integer)  # in seconds
    
    # User and audit
    created_by = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    created_by_user = db.relationship('User', back_populates='migrations')
    source_server = db.relationship('Server', foreign_keys=[source_server_id])
    target_server = db.relationship('Server', foreign_keys=[target_server_id])
    template = db.relationship('MigrationTemplate', back_populates='migrations')
    logs = db.relationship('MigrationLog', back_populates='migration', cascade='all, delete-orphan')
    
    def __init__(self, name, source_server_id, target_server_id, config, created_by, **kwargs):
        self.name = name
        self.source_server_id = source_server_id
        self.target_server_id = target_server_id
        self.config = config
        self.created_by = created_by
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def update_status(self, status, progress=None, current_step=None, error_message=None):
        """Update migration status and related fields"""
        self.status = status
        
        if progress is not None:
            self.progress = progress
        
        if current_step is not None:
            self.current_step = current_step
        
        if error_message is not None:
            self.error_message = error_message
        
        # Update timing based on status
        if status == 'running' and not self.started_at:
            self.started_at = datetime.utcnow()
        elif status in ['completed', 'failed', 'cancelled']:
            self.completed_at = datetime.utcnow()
        
        self.updated_at = datetime.utcnow()
    
    def get_duration(self):
        """Get migration duration in seconds"""
        if self.started_at:
            end_time = self.completed_at or datetime.utcnow()
            return int((end_time - self.started_at).total_seconds())
        return 0
    
    def is_active(self):
        """Check if migration is currently active"""
        return self.status in ['pending', 'running', 'paused']
    
    def can_be_cancelled(self):
        """Check if migration can be cancelled"""
        return self.status in ['pending', 'running', 'paused']
    
    def to_dict(self, include_config=False):
        """Convert migration to dictionary"""
        data = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'source_server': self.source_server.to_dict() if self.source_server else None,
            'target_server': self.target_server.to_dict() if self.target_server else None,
            'template': self.template.to_dict() if self.template else None,
            'status': self.status,
            'progress': self.progress,
            'current_step': self.current_step,
            'error_message': self.error_message,
            'scheduled_at': self.scheduled_at.isoformat() if self.scheduled_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'duration': self.get_duration(),
            'estimated_duration': self.estimated_duration,
            'created_by': self.created_by_user.get_full_name() if self.created_by_user else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_config:
            data['config'] = self.config
        
        return data
    
    def __repr__(self):
        return f'<Migration {self.name} ({self.status})>'

class MigrationTemplate(db.Model):
    __tablename__ = 'migration_templates'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(50))  # e.g., 'debian-to-debian', 'cloud-migration'
    
    # Template configuration
    config_template = db.Column(db.JSON, nullable=False)
    default_values = db.Column(db.JSON, default=dict)
    required_fields = db.Column(db.JSON, default=list)
    
    # Metadata
    is_active = db.Column(db.Boolean, default=True)
    version = db.Column(db.String(20), default='1.0')
    tags = db.Column(db.JSON, default=list)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    migrations = db.relationship('Migration', back_populates='template')
    
    def to_dict(self):
        """Convert template to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'config_template': self.config_template,
            'default_values': self.default_values,
            'required_fields': self.required_fields,
            'is_active': self.is_active,
            'version': self.version,
            'tags': self.tags,
            'usage_count': len(self.migrations),
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<MigrationTemplate {self.name}>'

class MigrationLog(db.Model):
    __tablename__ = 'migration_logs'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    migration_id = db.Column(db.String(36), db.ForeignKey('migrations.id'), nullable=False)
    
    # Log details
    level = db.Column(db.String(10), nullable=False)  # DEBUG, INFO, WARNING, ERROR
    message = db.Column(db.Text, nullable=False)
    step = db.Column(db.String(100))
    details = db.Column(db.JSON)  # Additional structured data
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    migration = db.relationship('Migration', back_populates='logs')
    
    def to_dict(self):
        """Convert log to dictionary"""
        return {
            'id': self.id,
            'migration_id': self.migration_id,
            'level': self.level,
            'message': self.message,
            'step': self.step,
            'details': self.details,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<MigrationLog {self.level}: {self.message[:50]}>'
