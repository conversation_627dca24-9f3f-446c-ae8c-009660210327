# Advanced Migration Configuration

# Server information
source_server: *************
target_server: *************
ssh_key: /path/to/ssh_key

# Migration method (tar or rsync)
migration_method: rsync
archive_path: /tmp/system_backup.tar.gz

# System preparation
stop_services: true
prepare_disk: true
target_disk: /dev/sda

# Target system configuration
target_hostname: new-server
root_password: new-secure-password

# User passwords to update
user_passwords:
  admin: admin-password
  user1: user1-password

# Network configuration
network:
  interface: eth0
  dhcp: false
  ip: *************
  prefix: 24
  netmask: *************
  gateway: ***********
  dns: *******,*******

# Directories to exclude from migration
exclude_dirs:
  - /var/log/*
  - /var/cache/*
  - /home/<USER>/Downloads/*
  - /home/<USER>/tmp/*

# Cloud-init configuration
use_cloud_init: false
default_user: admin
default_user_password_hash: $6$rounds=4096$salt$hashedpassword