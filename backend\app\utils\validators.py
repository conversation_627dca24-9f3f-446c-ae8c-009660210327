import re
import ipaddress
from typing import List, Optional

def validate_email(email: str) -> bool:
    """Validate email format"""
    if not email or len(email) > 254:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_password(password: str) -> List[str]:
    """Validate password strength and return list of errors"""
    errors = []
    
    if not password:
        errors.append("Password is required")
        return errors
    
    if len(password) < 8:
        errors.append("Password must be at least 8 characters long")
    
    if len(password) > 128:
        errors.append("Password must be less than 128 characters")
    
    if not re.search(r'[a-z]', password):
        errors.append("Password must contain at least one lowercase letter")
    
    if not re.search(r'[A-Z]', password):
        errors.append("Password must contain at least one uppercase letter")
    
    if not re.search(r'\d', password):
        errors.append("Password must contain at least one digit")
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        errors.append("Password must contain at least one special character")
    
    # Check for common weak passwords
    weak_passwords = [
        'password', '12345678', 'qwerty', 'abc123', 'password123',
        'admin', 'letmein', 'welcome', '123456789', 'password1'
    ]
    
    if password.lower() in weak_passwords:
        errors.append("Password is too common and easily guessable")
    
    return errors

def validate_username(username: str) -> List[str]:
    """Validate username format"""
    errors = []
    
    if not username:
        errors.append("Username is required")
        return errors
    
    if len(username) < 3:
        errors.append("Username must be at least 3 characters long")
    
    if len(username) > 50:
        errors.append("Username must be less than 50 characters")
    
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        errors.append("Username can only contain letters, numbers, underscores, and hyphens")
    
    if username.startswith('-') or username.endswith('-'):
        errors.append("Username cannot start or end with a hyphen")
    
    return errors

def validate_ip_address(ip: str) -> bool:
    """Validate IP address (IPv4 or IPv6)"""
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False

def validate_hostname(hostname: str) -> bool:
    """Validate hostname format"""
    if not hostname or len(hostname) > 253:
        return False
    
    # Remove trailing dot if present
    if hostname.endswith('.'):
        hostname = hostname[:-1]
    
    # Check each label
    labels = hostname.split('.')
    for label in labels:
        if not label or len(label) > 63:
            return False
        
        if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?$', label):
            return False
    
    return True

def validate_port(port: int) -> bool:
    """Validate port number"""
    return isinstance(port, int) and 1 <= port <= 65535

def validate_ssh_key(ssh_key: str) -> List[str]:
    """Validate SSH key format"""
    errors = []
    
    if not ssh_key:
        errors.append("SSH key is required")
        return errors
    
    # Check for common SSH key formats
    ssh_key = ssh_key.strip()
    
    # RSA key
    if ssh_key.startswith('ssh-rsa '):
        if len(ssh_key.split()) < 2:
            errors.append("Invalid RSA SSH key format")
    # Ed25519 key
    elif ssh_key.startswith('ssh-ed25519 '):
        if len(ssh_key.split()) < 2:
            errors.append("Invalid Ed25519 SSH key format")
    # ECDSA key
    elif ssh_key.startswith('ecdsa-sha2-'):
        if len(ssh_key.split()) < 2:
            errors.append("Invalid ECDSA SSH key format")
    # Private key (PEM format)
    elif '-----BEGIN' in ssh_key and '-----END' in ssh_key:
        if 'PRIVATE KEY' not in ssh_key:
            errors.append("Invalid private key format")
    else:
        errors.append("Unsupported SSH key format")
    
    return errors

def validate_migration_config(config: dict) -> List[str]:
    """Validate migration configuration"""
    errors = []
    
    if not isinstance(config, dict):
        errors.append("Configuration must be a dictionary")
        return errors
    
    # Required fields
    required_fields = ['migration_method']
    for field in required_fields:
        if field not in config:
            errors.append(f"Missing required field: {field}")
    
    # Validate migration method
    if 'migration_method' in config:
        valid_methods = ['rsync', 'tar']
        if config['migration_method'] not in valid_methods:
            errors.append(f"Invalid migration method. Must be one of: {', '.join(valid_methods)}")
    
    # Validate network configuration if present
    if 'network' in config and isinstance(config['network'], dict):
        network = config['network']
        
        if 'ip' in network and not validate_ip_address(network['ip']):
            errors.append("Invalid IP address in network configuration")
        
        if 'gateway' in network and not validate_ip_address(network['gateway']):
            errors.append("Invalid gateway IP address in network configuration")
        
        if 'prefix' in network:
            try:
                prefix = int(network['prefix'])
                if not (0 <= prefix <= 32):
                    errors.append("Network prefix must be between 0 and 32")
            except (ValueError, TypeError):
                errors.append("Network prefix must be a number")
    
    return errors

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    if not filename:
        return "unnamed"
    
    # Remove or replace dangerous characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    filename = re.sub(r'\.+', '.', filename)  # Replace multiple dots with single dot
    filename = filename.strip('. ')  # Remove leading/trailing dots and spaces
    
    # Limit length
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        max_name_length = 255 - len(ext) - 1 if ext else 255
        filename = name[:max_name_length] + ('.' + ext if ext else '')
    
    return filename or "unnamed"
