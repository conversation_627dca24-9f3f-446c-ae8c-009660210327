from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import User, Role, AuditLog
from app.utils.decorators import require_permission, admin_required, audit_log, handle_errors, validate_json
from app.utils.validators import validate_email, validate_password, validate_username

users_bp = Blueprint('users', __name__)

@users_bp.route('', methods=['GET'])
@jwt_required()
@require_permission('view_users')
@handle_errors
def get_users():
    """Get list of users with pagination and filtering"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')
    status_filter = request.args.get('status', '')
    
    query = User.query
    
    # Apply search filter
    if search:
        query = query.filter(
            (User.username.ilike(f'%{search}%')) |
            (User.email.ilike(f'%{search}%')) |
            (User.first_name.ilike(f'%{search}%')) |
            (User.last_name.ilike(f'%{search}%'))
        )
    
    # Apply role filter
    if role_filter:
        query = query.join(User.roles).filter(Role.name == role_filter)
    
    # Apply status filter
    if status_filter == 'active':
        query = query.filter(User.is_active == True)
    elif status_filter == 'inactive':
        query = query.filter(User.is_active == False)
    elif status_filter == 'verified':
        query = query.filter(User.is_verified == True)
    elif status_filter == 'unverified':
        query = query.filter(User.is_verified == False)
    
    # Order by creation date
    query = query.order_by(User.created_at.desc())
    
    # Paginate
    pagination = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    users = pagination.items
    
    return jsonify({
        'users': [user.to_dict() for user in users],
        'pagination': {
            'page': page,
            'pages': pagination.pages,
            'per_page': per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    }), 200

@users_bp.route('/<user_id>', methods=['GET'])
@jwt_required()
@require_permission('view_users')
@handle_errors
def get_user(user_id):
    """Get specific user details"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    current_user_id = get_jwt_identity()
    include_sensitive = (current_user_id == user_id or 
                        User.query.get(current_user_id).has_permission('manage_users'))
    
    return jsonify({
        'user': user.to_dict(include_sensitive=include_sensitive)
    }), 200

@users_bp.route('', methods=['POST'])
@jwt_required()
@require_permission('create_users')
@validate_json(['username', 'email', 'password', 'first_name', 'last_name'])
@audit_log('create_user', 'user')
@handle_errors
def create_user():
    """Create new user (admin only)"""
    data = request.get_json()
    
    # Validate input data
    username_errors = validate_username(data['username'])
    if username_errors:
        return jsonify({'error': 'Username validation failed', 'details': username_errors}), 400
    
    if not validate_email(data['email']):
        return jsonify({'error': 'Invalid email format'}), 400
    
    password_errors = validate_password(data['password'])
    if password_errors:
        return jsonify({'error': 'Password validation failed', 'details': password_errors}), 400
    
    # Check if user already exists
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': 'Username already exists'}), 409
    
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'error': 'Email already registered'}), 409
    
    # Create user
    user = User(
        username=data['username'],
        email=data['email'],
        password=data['password'],
        first_name=data['first_name'],
        last_name=data['last_name']
    )
    
    # Set additional fields if provided
    if 'is_active' in data:
        user.is_active = bool(data['is_active'])
    if 'is_verified' in data:
        user.is_verified = bool(data['is_verified'])
    
    # Assign roles if provided
    if 'roles' in data and isinstance(data['roles'], list):
        for role_name in data['roles']:
            role = Role.query.filter_by(name=role_name).first()
            if role:
                user.roles.append(role)
    else:
        # Assign default role
        default_role = Role.query.filter_by(name='user').first()
        if default_role:
            user.roles.append(default_role)
    
    db.session.add(user)
    db.session.commit()
    
    return jsonify({
        'message': 'User created successfully',
        'user': user.to_dict()
    }), 201

@users_bp.route('/<user_id>', methods=['PUT'])
@jwt_required()
@validate_json()
@audit_log('update_user', 'user')
@handle_errors
def update_user(user_id):
    """Update user information"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    current_user_id = get_jwt_identity()
    current_user = User.query.get(current_user_id)
    
    # Check permissions
    if (current_user_id != user_id and 
        not current_user.has_permission('manage_users')):
        return jsonify({'error': 'Permission denied'}), 403
    
    data = request.get_json()
    
    # Update basic fields
    if 'first_name' in data:
        user.first_name = data['first_name']
    if 'last_name' in data:
        user.last_name = data['last_name']
    
    # Email update (with validation)
    if 'email' in data:
        if not validate_email(data['email']):
            return jsonify({'error': 'Invalid email format'}), 400
        
        # Check if email is already taken by another user
        existing_user = User.query.filter_by(email=data['email']).first()
        if existing_user and existing_user.id != user_id:
            return jsonify({'error': 'Email already registered'}), 409
        
        user.email = data['email']
        user.is_verified = False  # Require re-verification
    
    # Admin-only fields
    if current_user.has_permission('manage_users'):
        if 'is_active' in data:
            user.is_active = bool(data['is_active'])
        if 'is_verified' in data:
            user.is_verified = bool(data['is_verified'])
        
        # Update roles
        if 'roles' in data and isinstance(data['roles'], list):
            user.roles.clear()
            for role_name in data['roles']:
                role = Role.query.filter_by(name=role_name).first()
                if role:
                    user.roles.append(role)
    
    db.session.commit()
    
    return jsonify({
        'message': 'User updated successfully',
        'user': user.to_dict()
    }), 200

@users_bp.route('/<user_id>', methods=['DELETE'])
@jwt_required()
@require_permission('delete_users')
@audit_log('delete_user', 'user')
@handle_errors
def delete_user(user_id):
    """Delete user (admin only)"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    current_user_id = get_jwt_identity()
    if current_user_id == user_id:
        return jsonify({'error': 'Cannot delete your own account'}), 400
    
    # Soft delete - just deactivate the user
    user.is_active = False
    db.session.commit()
    
    return jsonify({'message': 'User deactivated successfully'}), 200

@users_bp.route('/roles', methods=['GET'])
@jwt_required()
@require_permission('view_roles')
@handle_errors
def get_roles():
    """Get list of available roles"""
    roles = Role.query.order_by(Role.name).all()
    
    return jsonify({
        'roles': [role.to_dict() for role in roles]
    }), 200

@users_bp.route('/roles', methods=['POST'])
@jwt_required()
@admin_required
@validate_json(['name'])
@audit_log('create_role', 'role')
@handle_errors
def create_role():
    """Create new role (admin only)"""
    data = request.get_json()
    
    # Check if role already exists
    if Role.query.filter_by(name=data['name']).first():
        return jsonify({'error': 'Role already exists'}), 409
    
    role = Role(
        name=data['name'],
        description=data.get('description', ''),
        permissions=data.get('permissions', [])
    )
    
    db.session.add(role)
    db.session.commit()
    
    return jsonify({
        'message': 'Role created successfully',
        'role': role.to_dict()
    }), 201
