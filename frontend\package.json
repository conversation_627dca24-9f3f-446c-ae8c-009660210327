{"name": "linux-migration-frontend", "version": "1.0.0", "description": "Frontend for Linux Migration Service", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.16", "@mui/material": "^5.14.17", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "@reduxjs/toolkit": "^1.9.7", "axios": "^1.6.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-redux": "^8.1.3", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/node": "^16.18.59", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}