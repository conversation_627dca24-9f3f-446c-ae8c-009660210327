from functools import wraps
from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, verify_jwt_in_request
from app.models import User, AuditLog
from app import db
import traceback

def audit_log(action, resource_type):
    """Decorator to automatically log user actions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = None
            ip_address = request.remote_addr
            user_agent = request.headers.get('User-Agent', '')
            request_method = request.method
            request_path = request.path
            
            try:
                # Try to get user ID from JWT token
                verify_jwt_in_request(optional=True)
                user_id = get_jwt_identity()
            except Exception:
                pass  # No token or invalid token
            
            try:
                # Execute the original function
                result = f(*args, **kwargs)
                
                # Log successful action
                AuditLog.log_action(
                    action=action,
                    resource_type=resource_type,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_method=request_method,
                    request_path=request_path,
                    status='success'
                )
                
                return result
                
            except Exception as e:
                # Log failed action
                AuditLog.log_action(
                    action=action,
                    resource_type=resource_type,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_method=request_method,
                    request_path=request_path,
                    status='failed',
                    error_message=str(e)
                )
                
                # Re-raise the exception
                raise
        
        return decorated_function
    return decorator

def require_permission(permission):
    """Decorator to check if user has specific permission"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({'error': 'User not found'}), 404
            
            if not user.is_active:
                return jsonify({'error': 'Account is deactivated'}), 403
            
            if not user.has_permission(permission):
                return jsonify({'error': f'Permission denied. Required: {permission}'}), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def require_role(role_name):
    """Decorator to check if user has specific role"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({'error': 'User not found'}), 404
            
            if not user.is_active:
                return jsonify({'error': 'Account is deactivated'}), 403
            
            if not user.has_role(role_name):
                return jsonify({'error': f'Role required: {role_name}'}), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def admin_required(f):
    """Decorator to require admin role"""
    return require_role('admin')(f)

def handle_errors(f):
    """Decorator to handle common errors and return JSON responses"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            current_app.logger.warning(f"ValueError in {f.__name__}: {str(e)}")
            return jsonify({'error': 'Invalid input data', 'details': str(e)}), 400
        except KeyError as e:
            current_app.logger.warning(f"KeyError in {f.__name__}: {str(e)}")
            return jsonify({'error': 'Missing required field', 'field': str(e)}), 400
        except Exception as e:
            current_app.logger.error(f"Unexpected error in {f.__name__}: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            
            # Don't expose internal errors in production
            if current_app.config.get('DEBUG'):
                return jsonify({'error': 'Internal server error', 'details': str(e)}), 500
            else:
                return jsonify({'error': 'Internal server error'}), 500
    
    return decorated_function

def validate_json(required_fields=None):
    """Decorator to validate JSON request data"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({'error': 'Request must be JSON'}), 400
            
            data = request.get_json()
            if not data:
                return jsonify({'error': 'Invalid JSON data'}), 400
            
            if required_fields:
                missing_fields = []
                for field in required_fields:
                    if field not in data or data[field] is None:
                        missing_fields.append(field)
                
                if missing_fields:
                    return jsonify({
                        'error': 'Missing required fields',
                        'missing_fields': missing_fields
                    }), 400
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def rate_limit(max_requests=100, window=3600):
    """Simple rate limiting decorator using Redis"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                from app import redis_client
                
                # Create rate limit key
                user_id = None
                try:
                    verify_jwt_in_request(optional=True)
                    user_id = get_jwt_identity()
                except Exception:
                    pass
                
                key = f"rate_limit:{user_id or request.remote_addr}:{f.__name__}"
                
                # Check current count
                current_count = redis_client.get(key)
                if current_count and int(current_count) >= max_requests:
                    return jsonify({
                        'error': 'Rate limit exceeded',
                        'retry_after': redis_client.ttl(key)
                    }), 429
                
                # Increment counter
                pipe = redis_client.pipeline()
                pipe.incr(key)
                pipe.expire(key, window)
                pipe.execute()
                
                return f(*args, **kwargs)
                
            except Exception as e:
                # If Redis is not available, allow the request
                current_app.logger.warning(f"Rate limiting failed: {str(e)}")
                return f(*args, **kwargs)
        
        return decorated_function
    return decorator
