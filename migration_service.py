from flask import Flask, request, jsonify, render_template, send_file
import subprocess
import yaml
import os
import uuid
import threading
import logging
from datetime import datetime
import paramiko
import socket
import time

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['CONFIG_FOLDER'] = 'configs'
app.config['LOG_FOLDER'] = 'logs'

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("server.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Ensure directories exist
for folder in [app.config['UPLOAD_FOLDER'], app.config['CONFIG_FOLDER'], app.config['LOG_FOLDER']]:
    os.makedirs(folder, exist_ok=True)

# Store migration jobs
migrations = {}

class MigrationJob:
    def __init__(self, job_id, config):
        self.job_id = job_id
        self.config = config
        self.status = "pending"
        self.start_time = None
        self.end_time = None
        self.log_file = os.path.join(app.config['LOG_FOLDER'], f"{job_id}.log")
        self.progress = 0
        self.error = None
    
    def update_status(self, status, progress=None, error=None):
        self.status = status
        if progress is not None:
            self.progress = progress
        if error is not None:
            self.error = error
        
        if status == "running" and self.start_time is None:
            self.start_time = datetime.now()
        elif status in ["completed", "failed"]:
            self.end_time = datetime.now()

def run_migration(job_id):
    job = migrations[job_id]
    job.update_status("running")
    
    try:
        # Save configuration to file
        config_path = os.path.join(app.config['CONFIG_FOLDER'], f"{job_id}.yaml")
        with open(config_path, 'w') as f:
            yaml.dump(job.config, f)
        
        # Execute migration script
        cmd = ['./migrate-linux.sh', config_path]
        
        with open(job.log_file, 'w') as log_file:
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # Monitor progress
            for line in process.stdout:
                log_file.write(line)
                log_file.flush()
                
                # Update progress based on log output
                if "Creating system archive" in line:
                    job.update_status("running", 10)
                elif "Transferring system to target" in line:
                    job.update_status("running", 30)
                elif "Updating network configuration" in line:
                    job.update_status("running", 50)
                elif "Configuring bootloader" in line:
                    job.update_status("running", 70)
                elif "Finalizing migration" in line:
                    job.update_status("running", 90)
            
            process.wait()
            
            if process.returncode == 0:
                job.update_status("completed", 100)
            else:
                job.update_status("failed", progress=job.progress, error="Migration script failed")
    
    except Exception as e:
        logger.exception(f"Error in migration job {job_id}")
        job.update_status("failed", error=str(e))

def validate_server_connection(server, ssh_key):
    """Validate SSH connection to server"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(
            hostname=server,
            username='root',
            key_filename=ssh_key,
            timeout=5
        )
        client.close()
        return True, None
    except (paramiko.SSHException, socket.error) as e:
        return False, str(e)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/migrate', methods=['POST'])
def migrate_system():
    # Get migration parameters from request
    config = request.json
    
    # Validate required fields
    required_fields = ['source_server', 'target_server', 'ssh_key']
    for field in required_fields:
        if field not in config:
            return jsonify({'error': f'Missing required field: {field}'}), 400
    
    # Validate SSH connections
    source_valid, source_error = validate_server_connection(config['source_server'], config['ssh_key'])
    if not source_valid:
        return jsonify({'error': f'Cannot connect to source server: {source_error}'}), 400
    
    target_valid, target_error = validate_server_connection(config['target_server'], config['ssh_key'])
    if not target_valid:
        return jsonify({'error': f'Cannot connect to target server: {target_error}'}), 400
    
    # Create migration job
    job_id = str(uuid.uuid4())
    job = MigrationJob(job_id, config)
    migrations[job_id] = job
    
    # Start migration in background
    thread = threading.Thread(target=run_migration, args=(job_id,))
    thread.daemon = True
    thread.start()
    
    return jsonify({
        'job_id': job_id,
        'status': 'pending',
        'message': 'Migration job created successfully'
    })

@app.route('/api/jobs/<job_id>', methods=['GET'])
def get_job_status(job_id):
    if job_id not in migrations:
        return jsonify({'error': 'Job not found'}), 404
    
    job = migrations[job_id]
    
    return jsonify({
        'job_id': job.job_id,
        'status': job.status,
        'progress': job.progress,
        'start_time': job.start_time.isoformat() if job.start_time else None,
        'end_time': job.end_time.isoformat() if job.end_time else None,
        'error': job.error
    })

@app.route('/api/jobs/<job_id>/logs', methods=['GET'])
def get_job_logs(job_id):
    if job_id not in migrations:
        return jsonify({'error': 'Job not found'}), 404
    
    job = migrations[job_id]
    
    if not os.path.exists(job.log_file):
        return jsonify({'error': 'Log file not found'}), 404
    
    return send_file(job.log_file, mimetype='text/plain')

@app.route('/api/jobs', methods=['GET'])
def list_jobs():
    result = []
    for job_id, job in migrations.items():
        result.append({
            'job_id': job.job_id,
            'status': job.status,
            'progress': job.progress,
            'start_time': job.start_time.isoformat() if job.start_time else None,
            'end_time': job.end_time.isoformat() if job.end_time else None
        })
    
    return jsonify(result)

@app.route('/api/templates', methods=['GET'])
def get_templates():
    templates = [
        {
            'id': 'debian-to-debian',
            'name': 'Debian to Debian Migration',
            'description': 'Migrate between Debian/Ubuntu servers',
            'config': {
                'migration_method': 'rsync',
                'stop_services': True,
                'prepare_disk': False,
                'network': {
                    'interface': 'eth0',
                    'dhcp': False
                }
            }
        },
        {
            'id': 'centos-to-centos',
            'name': 'CentOS to CentOS Migration',
            'description': 'Migrate between CentOS/RHEL servers',
            'config': {
                'migration_method': 'tar',
                'stop_services': True,
                'prepare_disk': False,
                'network': {
                    'interface': 'eth0',
                    'dhcp': False
                }
            }
        },
        {
            'id': 'cloud-migration',
            'name': 'Cloud Server Migration',
            'description': 'Migrate to cloud with cloud-init',
            'config': {
                'migration_method': 'rsync',
                'stop_services': True,
                'prepare_disk': True,
                'use_cloud_init': True,
                'network': {
                    'dhcp': True
                }
            }
        }
    ]
    
    return jsonify(templates)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)