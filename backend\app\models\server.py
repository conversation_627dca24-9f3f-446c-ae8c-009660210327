from datetime import datetime
from app import db
import uuid

class Server(db.Model):
    __tablename__ = 'servers'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(200), nullable=False)
    hostname = db.Column(db.String(255), nullable=False)
    ip_address = db.Column(db.String(45), nullable=False)  # IPv4 or IPv6
    port = db.Column(db.Integer, default=22)
    
    # Server details
    os_type = db.Column(db.String(50))  # linux, windows
    os_distribution = db.Column(db.String(50))  # ubuntu, centos, debian, etc.
    os_version = db.Column(db.String(50))
    architecture = db.Column(db.String(20))  # x86_64, arm64, etc.
    
    # Authentication
    username = db.Column(db.String(100), default='root')
    ssh_key_path = db.Column(db.String(500))
    ssh_key_content = db.Column(db.Text)  # Encrypted SSH key content
    
    # Status and monitoring
    status = db.Column(db.String(20), default='unknown')  # online, offline, unknown, error
    last_check = db.Column(db.DateTime)
    last_online = db.Column(db.DateTime)
    
    # Server specifications
    cpu_cores = db.Column(db.Integer)
    memory_gb = db.Column(db.Float)
    disk_space_gb = db.Column(db.Float)
    
    # Grouping and tags
    group_id = db.Column(db.String(36), db.ForeignKey('server_groups.id'))
    tags = db.Column(db.JSON, default=list)
    
    # Metadata
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    group = db.relationship('ServerGroup', back_populates='servers')
    source_migrations = db.relationship('Migration', foreign_keys='Migration.source_server_id')
    target_migrations = db.relationship('Migration', foreign_keys='Migration.target_server_id')
    
    def __init__(self, name, hostname, ip_address, **kwargs):
        self.name = name
        self.hostname = hostname
        self.ip_address = ip_address
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def update_status(self, status, last_check=None):
        """Update server status"""
        self.status = status
        self.last_check = last_check or datetime.utcnow()
        
        if status == 'online':
            self.last_online = self.last_check
        
        self.updated_at = datetime.utcnow()
    
    def is_online(self):
        """Check if server is online"""
        return self.status == 'online'
    
    def get_connection_string(self):
        """Get SSH connection string"""
        return f"{self.username}@{self.ip_address}:{self.port}"
    
    def get_migration_count(self):
        """Get total number of migrations involving this server"""
        return len(self.source_migrations) + len(self.target_migrations)
    
    def to_dict(self, include_sensitive=False):
        """Convert server to dictionary"""
        data = {
            'id': self.id,
            'name': self.name,
            'hostname': self.hostname,
            'ip_address': self.ip_address,
            'port': self.port,
            'os_type': self.os_type,
            'os_distribution': self.os_distribution,
            'os_version': self.os_version,
            'architecture': self.architecture,
            'username': self.username,
            'status': self.status,
            'last_check': self.last_check.isoformat() if self.last_check else None,
            'last_online': self.last_online.isoformat() if self.last_online else None,
            'cpu_cores': self.cpu_cores,
            'memory_gb': self.memory_gb,
            'disk_space_gb': self.disk_space_gb,
            'group': self.group.to_dict() if self.group else None,
            'tags': self.tags,
            'description': self.description,
            'is_active': self.is_active,
            'migration_count': self.get_migration_count(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_sensitive:
            data.update({
                'ssh_key_path': self.ssh_key_path,
                'connection_string': self.get_connection_string()
            })
        
        return data
    
    def __repr__(self):
        return f'<Server {self.name} ({self.ip_address})>'

class ServerGroup(db.Model):
    __tablename__ = 'server_groups'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.Text)
    color = db.Column(db.String(7), default='#007bff')  # Hex color for UI
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    servers = db.relationship('Server', back_populates='group')
    
    def __init__(self, name, description=None, color=None):
        self.name = name
        self.description = description
        if color:
            self.color = color
    
    def get_server_count(self):
        """Get number of servers in this group"""
        return len(self.servers)
    
    def get_online_count(self):
        """Get number of online servers in this group"""
        return sum(1 for server in self.servers if server.is_online())
    
    def to_dict(self):
        """Convert group to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'color': self.color,
            'server_count': self.get_server_count(),
            'online_count': self.get_online_count(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<ServerGroup {self.name}>'
