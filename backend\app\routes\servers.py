from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import Server, ServerGroup, User, AuditLog
from app.utils.decorators import require_permission, audit_log, handle_errors, validate_json
from app.utils.validators import validate_ip_address, validate_hostname, validate_port, validate_ssh_key
import paramiko
import socket

servers_bp = Blueprint('servers', __name__)

@servers_bp.route('', methods=['GET'])
@jwt_required()
@require_permission('view_servers')
@handle_errors
def get_servers():
    """Get list of servers with pagination and filtering"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '')
    group_filter = request.args.get('group', '')
    status_filter = request.args.get('status', '')
    
    query = Server.query
    
    # Apply search filter
    if search:
        query = query.filter(
            (Server.name.ilike(f'%{search}%')) |
            (Server.hostname.ilike(f'%{search}%')) |
            (Server.ip_address.ilike(f'%{search}%'))
        )
    
    # Apply group filter
    if group_filter:
        query = query.filter(Server.group_id == group_filter)
    
    # Apply status filter
    if status_filter:
        query = query.filter(Server.status == status_filter)
    
    # Only active servers by default
    query = query.filter(Server.is_active == True)
    
    # Order by name
    query = query.order_by(Server.name)
    
    # Paginate
    pagination = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    servers = pagination.items
    
    return jsonify({
        'servers': [server.to_dict() for server in servers],
        'pagination': {
            'page': page,
            'pages': pagination.pages,
            'per_page': per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    }), 200

@servers_bp.route('/<server_id>', methods=['GET'])
@jwt_required()
@require_permission('view_servers')
@handle_errors
def get_server(server_id):
    """Get specific server details"""
    server = Server.query.get(server_id)
    if not server:
        return jsonify({'error': 'Server not found'}), 404
    
    current_user_id = get_jwt_identity()
    current_user = User.query.get(current_user_id)
    include_sensitive = current_user.has_permission('manage_servers')
    
    return jsonify({
        'server': server.to_dict(include_sensitive=include_sensitive)
    }), 200

@servers_bp.route('', methods=['POST'])
@jwt_required()
@require_permission('create_servers')
@validate_json(['name', 'hostname', 'ip_address'])
@audit_log('create_server', 'server')
@handle_errors
def create_server():
    """Create new server"""
    data = request.get_json()
    
    # Validate input data
    if not validate_hostname(data['hostname']) and not validate_ip_address(data['hostname']):
        return jsonify({'error': 'Invalid hostname format'}), 400
    
    if not validate_ip_address(data['ip_address']):
        return jsonify({'error': 'Invalid IP address format'}), 400
    
    port = data.get('port', 22)
    if not validate_port(port):
        return jsonify({'error': 'Invalid port number'}), 400
    
    # Validate SSH key if provided
    if 'ssh_key_content' in data and data['ssh_key_content']:
        ssh_key_errors = validate_ssh_key(data['ssh_key_content'])
        if ssh_key_errors:
            return jsonify({'error': 'SSH key validation failed', 'details': ssh_key_errors}), 400
    
    # Check if server with same IP already exists
    existing_server = Server.query.filter_by(ip_address=data['ip_address']).first()
    if existing_server:
        return jsonify({'error': 'Server with this IP address already exists'}), 409
    
    # Create server
    server = Server(
        name=data['name'],
        hostname=data['hostname'],
        ip_address=data['ip_address'],
        port=port,
        username=data.get('username', 'root'),
        ssh_key_path=data.get('ssh_key_path'),
        ssh_key_content=data.get('ssh_key_content'),
        os_type=data.get('os_type'),
        os_distribution=data.get('os_distribution'),
        os_version=data.get('os_version'),
        architecture=data.get('architecture'),
        group_id=data.get('group_id'),
        tags=data.get('tags', []),
        description=data.get('description')
    )
    
    db.session.add(server)
    db.session.commit()
    
    return jsonify({
        'message': 'Server created successfully',
        'server': server.to_dict()
    }), 201

@servers_bp.route('/<server_id>', methods=['PUT'])
@jwt_required()
@require_permission('manage_servers')
@validate_json()
@audit_log('update_server', 'server')
@handle_errors
def update_server(server_id):
    """Update server information"""
    server = Server.query.get(server_id)
    if not server:
        return jsonify({'error': 'Server not found'}), 404
    
    data = request.get_json()
    
    # Update basic fields
    if 'name' in data:
        server.name = data['name']
    if 'hostname' in data:
        if not validate_hostname(data['hostname']) and not validate_ip_address(data['hostname']):
            return jsonify({'error': 'Invalid hostname format'}), 400
        server.hostname = data['hostname']
    if 'ip_address' in data:
        if not validate_ip_address(data['ip_address']):
            return jsonify({'error': 'Invalid IP address format'}), 400
        
        # Check if IP is already taken by another server
        existing_server = Server.query.filter_by(ip_address=data['ip_address']).first()
        if existing_server and existing_server.id != server_id:
            return jsonify({'error': 'Server with this IP address already exists'}), 409
        
        server.ip_address = data['ip_address']
    
    if 'port' in data:
        if not validate_port(data['port']):
            return jsonify({'error': 'Invalid port number'}), 400
        server.port = data['port']
    
    if 'username' in data:
        server.username = data['username']
    
    if 'ssh_key_path' in data:
        server.ssh_key_path = data['ssh_key_path']
    
    if 'ssh_key_content' in data:
        if data['ssh_key_content']:
            ssh_key_errors = validate_ssh_key(data['ssh_key_content'])
            if ssh_key_errors:
                return jsonify({'error': 'SSH key validation failed', 'details': ssh_key_errors}), 400
        server.ssh_key_content = data['ssh_key_content']
    
    # Update other fields
    for field in ['os_type', 'os_distribution', 'os_version', 'architecture', 
                  'group_id', 'description', 'is_active']:
        if field in data:
            setattr(server, field, data[field])
    
    if 'tags' in data:
        server.tags = data['tags']
    
    db.session.commit()
    
    return jsonify({
        'message': 'Server updated successfully',
        'server': server.to_dict()
    }), 200

@servers_bp.route('/<server_id>', methods=['DELETE'])
@jwt_required()
@require_permission('delete_servers')
@audit_log('delete_server', 'server')
@handle_errors
def delete_server(server_id):
    """Delete server (soft delete)"""
    server = Server.query.get(server_id)
    if not server:
        return jsonify({'error': 'Server not found'}), 404
    
    # Check if server is used in any active migrations
    active_migrations = server.source_migrations + server.target_migrations
    active_migrations = [m for m in active_migrations if m.is_active()]
    
    if active_migrations:
        return jsonify({
            'error': 'Cannot delete server with active migrations',
            'active_migrations': len(active_migrations)
        }), 400
    
    # Soft delete - just deactivate the server
    server.is_active = False
    db.session.commit()
    
    return jsonify({'message': 'Server deactivated successfully'}), 200

@servers_bp.route('/<server_id>/test-connection', methods=['POST'])
@jwt_required()
@require_permission('manage_servers')
@audit_log('test_server_connection', 'server')
@handle_errors
def test_server_connection(server_id):
    """Test SSH connection to server"""
    server = Server.query.get(server_id)
    if not server:
        return jsonify({'error': 'Server not found'}), 404
    
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        # Use SSH key or password authentication
        if server.ssh_key_content:
            # Create temporary key file
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.pem') as key_file:
                key_file.write(server.ssh_key_content)
                key_file_path = key_file.name
            
            try:
                client.connect(
                    hostname=server.ip_address,
                    port=server.port,
                    username=server.username,
                    key_filename=key_file_path,
                    timeout=10
                )
            finally:
                os.unlink(key_file_path)
        
        elif server.ssh_key_path:
            client.connect(
                hostname=server.ip_address,
                port=server.port,
                username=server.username,
                key_filename=server.ssh_key_path,
                timeout=10
            )
        else:
            return jsonify({
                'status': 'error',
                'message': 'No SSH key configured for this server'
            }), 400
        
        # Test basic command
        stdin, stdout, stderr = client.exec_command('echo "Connection test successful"')
        output = stdout.read().decode().strip()
        
        client.close()
        
        # Update server status
        server.update_status('online')
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Connection successful',
            'output': output
        }), 200
        
    except (paramiko.SSHException, socket.error, Exception) as e:
        # Update server status
        server.update_status('error')
        db.session.commit()
        
        return jsonify({
            'status': 'error',
            'message': f'Connection failed: {str(e)}'
        }), 400

@servers_bp.route('/groups', methods=['GET'])
@jwt_required()
@require_permission('view_servers')
@handle_errors
def get_server_groups():
    """Get list of server groups"""
    groups = ServerGroup.query.order_by(ServerGroup.name).all()
    
    return jsonify({
        'groups': [group.to_dict() for group in groups]
    }), 200

@servers_bp.route('/groups', methods=['POST'])
@jwt_required()
@require_permission('manage_servers')
@validate_json(['name'])
@audit_log('create_server_group', 'server_group')
@handle_errors
def create_server_group():
    """Create new server group"""
    data = request.get_json()
    
    # Check if group already exists
    if ServerGroup.query.filter_by(name=data['name']).first():
        return jsonify({'error': 'Server group already exists'}), 409
    
    group = ServerGroup(
        name=data['name'],
        description=data.get('description', ''),
        color=data.get('color', '#007bff')
    )
    
    db.session.add(group)
    db.session.commit()
    
    return jsonify({
        'message': 'Server group created successfully',
        'group': group.to_dict()
    }), 201
