import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import serversReducer from './slices/serversSlice';
import migrationsReducer from './slices/migrationsSlice';
import uiReducer from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    servers: serversReducer,
    migrations: migrationsReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
