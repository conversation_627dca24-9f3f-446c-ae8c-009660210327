import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { 
  User, Server, Migration, MigrationTemplate, Role, 
  LoginCredentials, RegisterData, AuthResponse,
  ServerFormData, MigrationFormData, PaginatedResponse,
  DashboardStats
} from '@/types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as any;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              const response = await axios.post(`${this.baseURL}/auth/refresh`, {}, {
                headers: { Authorization: `Bearer ${refreshToken}` }
              });
              
              const { access_token } = response.data;
              localStorage.setItem('access_token', access_token);
              
              // Retry original request
              originalRequest.headers.Authorization = `Bearer ${access_token}`;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.logout();
            window.location.href = '/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Auth methods
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await this.api.post<AuthResponse>('/auth/login', credentials);
    const { access_token, refresh_token } = response.data;
    
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    
    return response.data;
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await this.api.post<AuthResponse>('/auth/register', data);
    const { access_token, refresh_token } = response.data;
    
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      // Ignore errors on logout
    } finally {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get<{ user: User }>('/auth/me');
    return response.data.user;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await this.api.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword
    });
  }

  // User methods
  async getUsers(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    role?: string;
    status?: string;
  }): Promise<PaginatedResponse<User>> {
    const response = await this.api.get('/users', { params });
    return {
      items: response.data.users,
      pagination: response.data.pagination
    };
  }

  async getUser(id: string): Promise<User> {
    const response = await this.api.get<{ user: User }>(`/users/${id}`);
    return response.data.user;
  }

  async createUser(data: Partial<User> & { password: string }): Promise<User> {
    const response = await this.api.post<{ user: User }>('/users', data);
    return response.data.user;
  }

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    const response = await this.api.put<{ user: User }>(`/users/${id}`, data);
    return response.data.user;
  }

  async deleteUser(id: string): Promise<void> {
    await this.api.delete(`/users/${id}`);
  }

  async getRoles(): Promise<Role[]> {
    const response = await this.api.get<{ roles: Role[] }>('/users/roles');
    return response.data.roles;
  }

  // Server methods
  async getServers(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    group?: string;
    status?: string;
  }): Promise<PaginatedResponse<Server>> {
    const response = await this.api.get('/servers', { params });
    return {
      items: response.data.servers,
      pagination: response.data.pagination
    };
  }

  async getServer(id: string): Promise<Server> {
    const response = await this.api.get<{ server: Server }>(`/servers/${id}`);
    return response.data.server;
  }

  async createServer(data: ServerFormData): Promise<Server> {
    const response = await this.api.post<{ server: Server }>('/servers', data);
    return response.data.server;
  }

  async updateServer(id: string, data: Partial<ServerFormData>): Promise<Server> {
    const response = await this.api.put<{ server: Server }>(`/servers/${id}`, data);
    return response.data.server;
  }

  async deleteServer(id: string): Promise<void> {
    await this.api.delete(`/servers/${id}`);
  }

  async testServerConnection(id: string): Promise<{ status: string; message: string }> {
    const response = await this.api.post(`/servers/${id}/test-connection`);
    return response.data;
  }

  // Migration methods
  async getMigrations(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    status?: string;
    source_server?: string;
    target_server?: string;
  }): Promise<PaginatedResponse<Migration>> {
    const response = await this.api.get('/migrations', { params });
    return {
      items: response.data.migrations,
      pagination: response.data.pagination
    };
  }

  async getMigration(id: string): Promise<Migration> {
    const response = await this.api.get<{ migration: Migration }>(`/migrations/${id}`);
    return response.data.migration;
  }

  async createMigration(data: MigrationFormData): Promise<Migration> {
    const response = await this.api.post<{ migration: Migration }>('/migrations', data);
    return response.data.migration;
  }

  async updateMigration(id: string, data: Partial<MigrationFormData>): Promise<Migration> {
    const response = await this.api.put<{ migration: Migration }>(`/migrations/${id}`, data);
    return response.data.migration;
  }

  async deleteMigration(id: string): Promise<void> {
    await this.api.delete(`/migrations/${id}`);
  }

  async startMigration(id: string): Promise<void> {
    await this.api.post(`/migrations/${id}/start`);
  }

  async pauseMigration(id: string): Promise<void> {
    await this.api.post(`/migrations/${id}/pause`);
  }

  async cancelMigration(id: string): Promise<void> {
    await this.api.post(`/migrations/${id}/cancel`);
  }

  async getMigrationLogs(id: string): Promise<string> {
    const response = await this.api.get(`/migrations/${id}/logs`, {
      responseType: 'text'
    });
    return response.data;
  }

  // Migration template methods
  async getMigrationTemplates(): Promise<MigrationTemplate[]> {
    const response = await this.api.get<{ templates: MigrationTemplate[] }>('/migrations/templates');
    return response.data.templates;
  }

  // Dashboard methods
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.api.get<DashboardStats>('/dashboard/stats');
    return response.data;
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }

  getAuthToken(): string | null {
    return localStorage.getItem('access_token');
  }
}

export const apiService = new ApiService();
export default apiService;
