<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux System Migration Service</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .step-container {
            position: relative;
            padding-bottom: 30px;
        }
        .step-container:before {
            content: '';
            position: absolute;
            left: 25px;
            top: 0;
            height: 100%;
            width: 2px;
            background: #ccc;
        }
        .step {
            position: relative;
            padding-left: 60px;
            margin-bottom: 20px;
        }
        .step:before {
            content: '';
            position: absolute;
            left: 15px;
            top: 5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #007bff;
        }
        .step.active:before {
            background: #28a745;
        }
        .step.error:before {
            background: #dc3545;
        }
        .log-container {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Linux System Migration Service</h1>
        
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="new-tab" data-bs-toggle="tab" data-bs-target="#new" type="button" role="tab">New Migration</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="jobs-tab" data-bs-toggle="tab" data-bs-target="#jobs" type="button" role="tab">Active Jobs</button>
            </li>
        </ul>
        
        <div class="tab-content mt-3" id="myTabContent">
            <!-- New Migration Tab -->
            <div class="tab-pane fade show active" id="new" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5>Migration Configuration</h5>
                            </div>
                            <div class="card-body">
                                <form id="migrationForm">
                                    <h6 class="mb-3">Server Information</h6>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="sourceServer" class="form-label">Source Server IP</label>
                                            <input type="text" class="form-control" id="sourceServer" required>
                                        </div>
                                        <div class="
</augment_code_snippet>