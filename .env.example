# Environment Configuration
NODE_ENV=production
FLASK_ENV=production

# Database Configuration
DATABASE_URL=************************************************************/migration_service
POSTGRES_DB=migration_service
POSTGRES_USER=migration_user
POSTGRES_PASSWORD=migration_password

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# Security Keys (CHANGE THESE IN PRODUCTION!)
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production

# Email Configuration (Optional)
MAIL_SERVER=mail.altsolo.net
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=hgtfdg#7zFD@6
MAIL_DEFAULT_SENDER=<EMAIL>

# File Storage
UPLOAD_FOLDER=uploads
LOG_FOLDER=logs
CONFIG_FOLDER=configs
MIGRATION_SCRIPTS_PATH=scripts

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000/api

# SSL Configuration (for production)
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Monitoring
SENTRY_DSN=your-sentry-dsn-here
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600
