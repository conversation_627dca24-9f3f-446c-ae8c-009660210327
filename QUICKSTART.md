# 🚀 Быстрый запуск Linux Migration Service

## Исправление ошибок Docker

### Ошибка npm ci в backend
Если вы получили ошибку:
```
=> ERROR [build 4/6] RUN npm ci --only=production
```

Это было исправлено! Backend теперь использует Python, а не Node.js команды.

### Ошибка chmod scripts
Если вы получили ошибку:
```
=> ERROR [celery  9/10] RUN chmod +x scripts/*.sh
```

Это также исправлено! Скрипты перемещены в правильную директорию.

## 📋 Предварительные требования

- Docker и Docker Compose
- 4GB RAM (минимум)
- 10GB свободного места на диске

## 🚀 Запуск за 3 шага

### 1. Настройка переменных окружения

```bash
# Скопируйте файл с переменными окружения
cp .env.example .env

# ВАЖНО: Измените секретные ключи в .env файле!
# Откройте .env и замените:
# SECRET_KEY=your-super-secret-key-change-this-in-production
# JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
```

### 2. Запуск всех сервисов

```bash
# Запуск в фоновом режиме
docker-compose up -d

# Просмотр логов (опционально)
docker-compose logs -f
```

### 3. Инициализация базы данных

```bash
# Создание таблиц и администратора
docker-compose exec backend python app.py init-db
```

## 🎉 Готово!

Откройте браузер и перейдите по адресу: **http://localhost**

### Данные для входа:
- **Логин**: `admin`
- **Пароль**: `admin123`

⚠️ **ОБЯЗАТЕЛЬНО смените пароль после первого входа!**

## 📊 Доступные сервисы

- **Веб-интерфейс**: http://localhost
- **API**: http://localhost/api
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## 🛠️ Полезные команды

```bash
# Остановка всех сервисов
docker-compose down

# Перезапуск конкретного сервиса
docker-compose restart backend

# Просмотр логов конкретного сервиса
docker-compose logs -f backend

# Подключение к базе данных
docker-compose exec postgres psql -U migration_user -d migration_service

# Подключение к Redis
docker-compose exec redis redis-cli

# Выполнение команд в backend контейнере
docker-compose exec backend bash

# Запуск в режиме разработки
docker-compose -f docker-compose.dev.yml up -d

# Пересборка контейнеров после изменений
docker-compose build --no-cache
```

## 🔧 Разработка

### Backend (Flask)

```bash
cd backend

# Создание виртуального окружения
python -m venv venv
source venv/bin/activate  # Linux/Mac
# или
venv\Scripts\activate     # Windows

# Установка зависимостей
pip install -r requirements.txt

# Запуск в режиме разработки
python app.py
```

### Frontend (React)

```bash
cd frontend

# Установка зависимостей
npm install

# Запуск в режиме разработки
npm start
```

## 🐛 Решение проблем

### Ошибка "Port already in use"
```bash
# Найти процесс, использующий порт
netstat -tulpn | grep :5000

# Остановить Docker Compose
docker-compose down

# Перезапустить
docker-compose up -d
```

### Ошибка подключения к базе данных
```bash
# Проверить статус контейнеров
docker-compose ps

# Перезапустить PostgreSQL
docker-compose restart postgres

# Проверить логи
docker-compose logs postgres
```

### Ошибка "Permission denied" для скриптов
```bash
# Исправить права доступа
chmod +x backend/scripts/*.sh
```

## 📚 Дополнительная информация

- **Полная документация**: [README.md](README.md)
- **API документация**: http://localhost/api/docs (после запуска)
- **Архитектура проекта**: См. README.md

## 🆘 Поддержка

Если возникли проблемы:

1. Проверьте логи: `docker-compose logs`
2. Убедитесь, что все порты свободны
3. Проверьте, что Docker запущен
4. Создайте Issue в GitHub

---

**Удачного использования! 🚀**
