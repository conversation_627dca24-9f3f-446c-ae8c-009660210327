version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: migration_postgres_dev
    environment:
      POSTGRES_DB: migration_service
      POSTGRES_USER: migration_user
      POSTGRES_PASSWORD: migration_password
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - migration_network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: migration_redis_dev
    command: redis-server --appendonly yes
    volumes:
      - redis_data_dev:/data
    ports:
      - "6379:6379"
    networks:
      - migration_network
    restart: unless-stopped

  # Backend API (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: migration_backend_dev
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=************************************************************/migration_service
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=dev-secret-key
      - JWT_SECRET_KEY=dev-jwt-secret-key
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./backend/configs:/app/configs
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    networks:
      - migration_network
    restart: unless-stopped
    command: python app.py

volumes:
  postgres_data_dev:
  redis_data_dev:

networks:
  migration_network:
    driver: bridge
