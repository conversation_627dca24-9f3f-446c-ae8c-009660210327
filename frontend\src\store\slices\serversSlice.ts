import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Server, ServerFormData, PaginatedResponse } from '@/types';
import apiService from '@/services/api';

interface ServersState {
  servers: Server[];
  currentServer: Server | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    pages: number;
    per_page: number;
    total: number;
    has_next: boolean;
    has_prev: boolean;
  } | null;
}

const initialState: ServersState = {
  servers: [],
  currentServer: null,
  isLoading: false,
  error: null,
  pagination: null,
};

// Async thunks
export const fetchServers = createAsyncThunk(
  'servers/fetchServers',
  async (params?: {
    page?: number;
    per_page?: number;
    search?: string;
    group?: string;
    status?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await apiService.getServers(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch servers');
    }
  }
);

export const fetchServer = createAsyncThunk(
  'servers/fetchServer',
  async (id: string, { rejectWithValue }) => {
    try {
      const server = await apiService.getServer(id);
      return server;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch server');
    }
  }
);

export const createServer = createAsyncThunk(
  'servers/createServer',
  async (data: ServerFormData, { rejectWithValue }) => {
    try {
      const server = await apiService.createServer(data);
      return server;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create server');
    }
  }
);

export const updateServer = createAsyncThunk(
  'servers/updateServer',
  async ({ id, data }: { id: string; data: Partial<ServerFormData> }, { rejectWithValue }) => {
    try {
      const server = await apiService.updateServer(id, data);
      return server;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update server');
    }
  }
);

export const deleteServer = createAsyncThunk(
  'servers/deleteServer',
  async (id: string, { rejectWithValue }) => {
    try {
      await apiService.deleteServer(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete server');
    }
  }
);

export const testServerConnection = createAsyncThunk(
  'servers/testConnection',
  async (id: string, { rejectWithValue }) => {
    try {
      const result = await apiService.testServerConnection(id);
      return { id, result };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Connection test failed');
    }
  }
);

const serversSlice = createSlice({
  name: 'servers',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentServer: (state) => {
      state.currentServer = null;
    },
    updateServerStatus: (state, action: PayloadAction<{ id: string; status: string }>) => {
      const { id, status } = action.payload;
      const server = state.servers.find(s => s.id === id);
      if (server) {
        server.status = status as any;
      }
      if (state.currentServer?.id === id) {
        state.currentServer.status = status as any;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch servers
      .addCase(fetchServers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchServers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.servers = action.payload.items;
        state.pagination = action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchServers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch server
      .addCase(fetchServer.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchServer.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentServer = action.payload;
        state.error = null;
      })
      .addCase(fetchServer.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Create server
      .addCase(createServer.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createServer.fulfilled, (state, action) => {
        state.isLoading = false;
        state.servers.unshift(action.payload);
        state.error = null;
      })
      .addCase(createServer.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Update server
      .addCase(updateServer.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateServer.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.servers.findIndex(s => s.id === action.payload.id);
        if (index !== -1) {
          state.servers[index] = action.payload;
        }
        if (state.currentServer?.id === action.payload.id) {
          state.currentServer = action.payload;
        }
        state.error = null;
      })
      .addCase(updateServer.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Delete server
      .addCase(deleteServer.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteServer.fulfilled, (state, action) => {
        state.isLoading = false;
        state.servers = state.servers.filter(s => s.id !== action.payload);
        if (state.currentServer?.id === action.payload) {
          state.currentServer = null;
        }
        state.error = null;
      })
      .addCase(deleteServer.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Test connection
      .addCase(testServerConnection.fulfilled, (state, action) => {
        const { id, result } = action.payload;
        const server = state.servers.find(s => s.id === id);
        if (server && result.status === 'success') {
          server.status = 'online';
        }
      });
  },
});

export const { clearError, clearCurrentServer, updateServerStatus } = serversSlice.actions;
export default serversSlice.reducer;
