// User types
export interface User {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  is_active: boolean;
  is_verified: boolean;
  last_login: string | null;
  created_at: string;
  roles: string[];
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  created_at: string;
  user_count: number;
}

// Server types
export interface Server {
  id: string;
  name: string;
  hostname: string;
  ip_address: string;
  port: number;
  os_type?: string;
  os_distribution?: string;
  os_version?: string;
  architecture?: string;
  username: string;
  status: 'online' | 'offline' | 'unknown' | 'error';
  last_check?: string;
  last_online?: string;
  cpu_cores?: number;
  memory_gb?: number;
  disk_space_gb?: number;
  group?: ServerGroup;
  tags: string[];
  description?: string;
  is_active: boolean;
  migration_count: number;
  created_at: string;
  updated_at: string;
}

export interface ServerGroup {
  id: string;
  name: string;
  description?: string;
  color: string;
  server_count: number;
  online_count: number;
  created_at: string;
  updated_at: string;
}

// Migration types
export interface Migration {
  id: string;
  name: string;
  description?: string;
  source_server: Server;
  target_server: Server;
  template?: MigrationTemplate;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';
  progress: number;
  current_step?: string;
  error_message?: string;
  scheduled_at?: string;
  started_at?: string;
  completed_at?: string;
  duration: number;
  estimated_duration?: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface MigrationTemplate {
  id: string;
  name: string;
  description?: string;
  category?: string;
  config_template: Record<string, any>;
  default_values: Record<string, any>;
  required_fields: string[];
  is_active: boolean;
  version: string;
  tags: string[];
  usage_count: number;
  created_at: string;
}

export interface MigrationLog {
  id: string;
  migration_id: string;
  level: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR';
  message: string;
  step?: string;
  details?: Record<string, any>;
  created_at: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  details?: any;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    pages: number;
    per_page: number;
    total: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Auth types
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  first_name: string;
  last_name: string;
}

export interface AuthResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  message: string;
}

// Form types
export interface ServerFormData {
  name: string;
  hostname: string;
  ip_address: string;
  port: number;
  username: string;
  ssh_key_path?: string;
  ssh_key_content?: string;
  os_type?: string;
  os_distribution?: string;
  os_version?: string;
  architecture?: string;
  group_id?: string;
  tags: string[];
  description?: string;
}

export interface MigrationFormData {
  name: string;
  description?: string;
  source_server_id: string;
  target_server_id: string;
  template_id?: string;
  config: Record<string, any>;
  scheduled_at?: string;
}

// Dashboard types
export interface DashboardStats {
  total_servers: number;
  online_servers: number;
  total_migrations: number;
  active_migrations: number;
  completed_migrations: number;
  failed_migrations: number;
  recent_migrations: Migration[];
  server_status_distribution: Record<string, number>;
  migration_status_distribution: Record<string, number>;
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Socket.IO event types
export interface SocketEvents {
  migration_update: (data: { migration_id: string; status: string; progress: number }) => void;
  migration_log: (data: MigrationLog) => void;
  server_status_update: (data: { server_id: string; status: string }) => void;
  notification: (data: Notification) => void;
}
