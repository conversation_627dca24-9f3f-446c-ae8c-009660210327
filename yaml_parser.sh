#!/bin/bash
# Simple YAML parser for bash

YAML_FILE=""
YAML_CONTENT=""

function parse_yaml() {
    YAML_FILE="$1"
    YAML_CONTENT=$(cat "$YAML_FILE")
}

function get_yaml_value() {
    local key="$1"
    local default="$2"
    
    # Handle nested keys (e.g., network.interface)
    if [[ "$key" == *"."* ]]; then
        local parent_key="${key%%.*}"
        local child_key="${key#*.}"
        local parent_section=$(echo "$YAML_CONTENT" | awk "/^$parent_key:/,/^[a-zA-Z]/" | grep -v "^[a-zA-Z]" | grep -v "^$parent_key:")
        local value=$(echo "$parent_section" | grep "^\s*$child_key:" | cut -d ':' -f2- | sed 's/^[ \t]*//;s/[ \t]*$//')
    else
        local value=$(echo "$YAML_CONTENT" | grep "^$key:" | cut -d ':' -f2- | sed 's/^[ \t]*//;s/[ \t]*$//')
    fi
    
    if [[ -z "$value" && -n "$default" ]]; then
        echo "$default"
    else
        echo "$value"
    fi
}

function get_yaml_array() {
    local key="$1"
    local array_section=$(echo "$YAML_CONTENT" | awk "/^$key:/,/^[a-zA-Z]/" | grep -v "^[a-zA-Z]" | grep -v "^$key:")
    
    echo "$array_section" | grep "^\s*-" | sed 's/^\s*-\s*//'
}

function get_yaml_map() {
    local key="$1"
    local map_section=$(echo "$YAML_CONTENT" | awk "/^$key:/,/^[a-zA-Z]/" | grep -v "^[a-zA-Z]" | grep -v "^$key:")
    
    declare -A result
    while read -r line; do
        if [[ "$line" =~ ^\s*([a-zA-Z0-9_-]+):\s*(.*)$ ]]; then
            local map_key="${BASH_REMATCH[1]}"
            local map_value="${BASH_REMATCH[2]}"
            result["$map_key"]="$map_value"
            echo "$map_key:$map_value"
        fi
    done <<< "$map_section"
}