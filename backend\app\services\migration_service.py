import os
import subprocess
import threading
import yaml
import time
from datetime import datetime
from flask import current_app
from app import db, socketio, redis_client
from app.models import Migration, MigrationLog, Server
import logging

logger = logging.getLogger(__name__)

class MigrationService:
    """Service for managing migration operations"""
    
    def __init__(self):
        self.active_migrations = {}
        self.migration_processes = {}
    
    def start_migration(self, migration_id):
        """Start a migration process"""
        migration = Migration.query.get(migration_id)
        if not migration:
            raise ValueError("Migration not found")
        
        if migration.status != 'pending':
            raise ValueError("Migration is not in pending state")
        
        # Update migration status
        migration.update_status('running', 0, 'Initializing migration...')
        db.session.commit()
        
        # Start migration in background thread
        thread = threading.Thread(
            target=self._run_migration,
            args=(migration_id,),
            daemon=True
        )
        thread.start()
        
        self.active_migrations[migration_id] = thread
        
        # Emit real-time update
        self._emit_migration_update(migration_id, 'running', 0)
        
        logger.info(f"Started migration {migration_id}")
    
    def pause_migration(self, migration_id):
        """Pause a running migration"""
        migration = Migration.query.get(migration_id)
        if not migration:
            raise ValueError("Migration not found")
        
        if migration.status != 'running':
            raise ValueError("Migration is not running")
        
        # Set pause flag in Redis
        redis_client.set(f"migration_pause_{migration_id}", "true", ex=3600)
        
        # Update migration status
        migration.update_status('paused', migration.progress, 'Migration paused by user')
        db.session.commit()
        
        # Emit real-time update
        self._emit_migration_update(migration_id, 'paused', migration.progress)
        
        logger.info(f"Paused migration {migration_id}")
    
    def cancel_migration(self, migration_id):
        """Cancel a migration"""
        migration = Migration.query.get(migration_id)
        if not migration:
            raise ValueError("Migration not found")
        
        if not migration.can_be_cancelled():
            raise ValueError("Migration cannot be cancelled in current state")
        
        # Set cancel flag in Redis
        redis_client.set(f"migration_cancel_{migration_id}", "true", ex=3600)
        
        # Kill process if running
        if migration_id in self.migration_processes:
            try:
                process = self.migration_processes[migration_id]
                process.terminate()
                process.wait(timeout=10)
            except Exception as e:
                logger.warning(f"Failed to terminate migration process {migration_id}: {e}")
                try:
                    process.kill()
                except Exception:
                    pass
        
        # Update migration status
        migration.update_status('cancelled', migration.progress, 'Migration cancelled by user')
        db.session.commit()
        
        # Cleanup
        self._cleanup_migration(migration_id)
        
        # Emit real-time update
        self._emit_migration_update(migration_id, 'cancelled', migration.progress)
        
        logger.info(f"Cancelled migration {migration_id}")
    
    def _run_migration(self, migration_id):
        """Run migration in background thread"""
        try:
            migration = Migration.query.get(migration_id)
            if not migration:
                logger.error(f"Migration {migration_id} not found")
                return
            
            self._log_migration(migration_id, 'INFO', 'Starting migration process')
            
            # Create configuration file
            config_path = self._create_config_file(migration)
            
            # Execute migration script
            script_path = os.path.join(
                current_app.config['MIGRATION_SCRIPTS_PATH'],
                'migrate-linux.sh'
            )
            
            if not os.path.exists(script_path):
                raise FileNotFoundError(f"Migration script not found: {script_path}")
            
            # Start migration process
            cmd = ['bash', script_path, config_path]
            
            self._log_migration(migration_id, 'INFO', f'Executing command: {" ".join(cmd)}')
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                cwd=os.path.dirname(script_path)
            )
            
            self.migration_processes[migration_id] = process
            
            # Monitor process output
            self._monitor_migration_process(migration_id, process)
            
        except Exception as e:
            logger.exception(f"Error in migration {migration_id}")
            self._handle_migration_error(migration_id, str(e))
        finally:
            self._cleanup_migration(migration_id)
    
    def _monitor_migration_process(self, migration_id, process):
        """Monitor migration process output and update progress"""
        migration = Migration.query.get(migration_id)
        
        try:
            for line in process.stdout:
                line = line.strip()
                if not line:
                    continue
                
                # Check for cancellation
                if redis_client.get(f"migration_cancel_{migration_id}"):
                    self._log_migration(migration_id, 'INFO', 'Migration cancellation requested')
                    process.terminate()
                    break
                
                # Check for pause
                while redis_client.get(f"migration_pause_{migration_id}"):
                    time.sleep(1)
                    if redis_client.get(f"migration_cancel_{migration_id}"):
                        process.terminate()
                        break
                
                # Log output
                self._log_migration(migration_id, 'INFO', line)
                
                # Update progress based on output
                progress = self._parse_progress(line)
                if progress is not None:
                    step = self._parse_step(line)
                    migration.update_status('running', progress, step)
                    db.session.commit()
                    
                    # Emit real-time update
                    self._emit_migration_update(migration_id, 'running', progress, step)
            
            # Wait for process to complete
            return_code = process.wait()
            
            # Update final status
            if return_code == 0:
                migration.update_status('completed', 100, 'Migration completed successfully')
                self._log_migration(migration_id, 'INFO', 'Migration completed successfully')
                self._emit_migration_update(migration_id, 'completed', 100)
            else:
                error_msg = f'Migration failed with exit code {return_code}'
                migration.update_status('failed', migration.progress, error_msg)
                self._log_migration(migration_id, 'ERROR', error_msg)
                self._emit_migration_update(migration_id, 'failed', migration.progress)
            
            db.session.commit()
            
        except Exception as e:
            logger.exception(f"Error monitoring migration {migration_id}")
            self._handle_migration_error(migration_id, str(e))
    
    def _create_config_file(self, migration):
        """Create YAML configuration file for migration"""
        config_dir = current_app.config['CONFIG_FOLDER']
        os.makedirs(config_dir, exist_ok=True)
        
        config_path = os.path.join(config_dir, f"{migration.id}.yaml")
        
        # Prepare configuration
        config = {
            'source_server': migration.source_server.ip_address,
            'target_server': migration.target_server.ip_address,
            'ssh_key': migration.source_server.ssh_key_path or '/tmp/ssh_key',
            **migration.config
        }
        
        # Write SSH key content to temporary file if needed
        if migration.source_server.ssh_key_content and not migration.source_server.ssh_key_path:
            ssh_key_path = f"/tmp/ssh_key_{migration.id}"
            with open(ssh_key_path, 'w') as f:
                f.write(migration.source_server.ssh_key_content)
            os.chmod(ssh_key_path, 0o600)
            config['ssh_key'] = ssh_key_path
        
        # Write configuration file
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        return config_path
    
    def _parse_progress(self, line):
        """Parse progress from migration output"""
        progress_indicators = {
            'Creating system archive': 10,
            'Transferring system to target': 30,
            'Updating network configuration': 50,
            'Configuring bootloader': 70,
            'Finalizing migration': 90,
            'Migration completed': 100
        }
        
        for indicator, progress in progress_indicators.items():
            if indicator in line:
                return progress
        
        return None
    
    def _parse_step(self, line):
        """Parse current step from migration output"""
        if 'Creating system archive' in line:
            return 'Creating system archive'
        elif 'Transferring system' in line:
            return 'Transferring system'
        elif 'Updating network' in line:
            return 'Updating network configuration'
        elif 'Configuring bootloader' in line:
            return 'Configuring bootloader'
        elif 'Finalizing' in line:
            return 'Finalizing migration'
        
        return None
    
    def _log_migration(self, migration_id, level, message, step=None, details=None):
        """Log migration message to database"""
        try:
            log_entry = MigrationLog(
                migration_id=migration_id,
                level=level,
                message=message,
                step=step,
                details=details
            )
            
            db.session.add(log_entry)
            db.session.commit()
            
            # Emit real-time log update
            socketio.emit(
                'migration_log',
                log_entry.to_dict(),
                room=f"migration_{migration_id}"
            )
            
        except Exception as e:
            logger.error(f"Failed to log migration message: {e}")
    
    def _emit_migration_update(self, migration_id, status, progress, step=None):
        """Emit real-time migration update"""
        try:
            socketio.emit(
                'migration_update',
                {
                    'migration_id': migration_id,
                    'status': status,
                    'progress': progress,
                    'step': step,
                    'timestamp': datetime.utcnow().isoformat()
                },
                room=f"migration_{migration_id}"
            )
        except Exception as e:
            logger.error(f"Failed to emit migration update: {e}")
    
    def _handle_migration_error(self, migration_id, error_message):
        """Handle migration error"""
        try:
            migration = Migration.query.get(migration_id)
            if migration:
                migration.update_status('failed', migration.progress, error_message)
                db.session.commit()
            
            self._log_migration(migration_id, 'ERROR', error_message)
            self._emit_migration_update(migration_id, 'failed', migration.progress if migration else 0)
            
        except Exception as e:
            logger.error(f"Failed to handle migration error: {e}")
    
    def _cleanup_migration(self, migration_id):
        """Cleanup migration resources"""
        try:
            # Remove from active migrations
            if migration_id in self.active_migrations:
                del self.active_migrations[migration_id]
            
            # Remove from processes
            if migration_id in self.migration_processes:
                del self.migration_processes[migration_id]
            
            # Clean up Redis flags
            redis_client.delete(f"migration_pause_{migration_id}")
            redis_client.delete(f"migration_cancel_{migration_id}")
            
            # Clean up temporary files
            config_path = os.path.join(current_app.config['CONFIG_FOLDER'], f"{migration_id}.yaml")
            if os.path.exists(config_path):
                os.remove(config_path)
            
            ssh_key_path = f"/tmp/ssh_key_{migration_id}"
            if os.path.exists(ssh_key_path):
                os.remove(ssh_key_path)
            
        except Exception as e:
            logger.error(f"Failed to cleanup migration {migration_id}: {e}")
    
    def get_active_migrations(self):
        """Get list of active migration IDs"""
        return list(self.active_migrations.keys())
    
    def is_migration_active(self, migration_id):
        """Check if migration is currently active"""
        return migration_id in self.active_migrations
