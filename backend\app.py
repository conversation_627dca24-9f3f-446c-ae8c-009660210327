#!/usr/bin/env python3
"""
Linux Migration Service - Main Application
"""

import os
import sys
from app import create_app, db, socketio
from app.models import User, Role

def create_default_roles():
    """Create default roles if they don't exist"""
    roles_data = [
        {
            'name': 'admin',
            'description': 'Full system administrator',
            'permissions': [
                'view_users', 'create_users', 'manage_users', 'delete_users',
                'view_roles', 'create_roles', 'manage_roles', 'delete_roles',
                'view_servers', 'create_servers', 'manage_servers', 'delete_servers',
                'view_migrations', 'create_migrations', 'manage_migrations', 'delete_migrations',
                'view_audit_logs', 'manage_system_settings'
            ]
        },
        {
            'name': 'operator',
            'description': 'Migration operator with limited admin rights',
            'permissions': [
                'view_users', 'view_servers', 'create_servers', 'manage_servers',
                'view_migrations', 'create_migrations', 'manage_migrations'
            ]
        },
        {
            'name': 'user',
            'description': 'Regular user with basic permissions',
            'permissions': [
                'view_servers', 'view_migrations', 'create_migrations'
            ]
        },
        {
            'name': 'viewer',
            'description': 'Read-only access',
            'permissions': [
                'view_servers', 'view_migrations'
            ]
        }
    ]
    
    for role_data in roles_data:
        role = Role.query.filter_by(name=role_data['name']).first()
        if not role:
            role = Role(
                name=role_data['name'],
                description=role_data['description'],
                permissions=role_data['permissions']
            )
            db.session.add(role)
            print(f"Created role: {role_data['name']}")
        else:
            # Update permissions for existing roles
            role.permissions = role_data['permissions']
            print(f"Updated role: {role_data['name']}")
    
    db.session.commit()

def create_admin_user():
    """Create default admin user if it doesn't exist"""
    admin_user = User.query.filter_by(username='admin').first()
    
    if not admin_user:
        admin_role = Role.query.filter_by(name='admin').first()
        
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            password='admin123',  # Change this in production!
            first_name='System',
            last_name='Administrator'
        )
        admin_user.is_verified = True
        
        if admin_role:
            admin_user.roles.append(admin_role)
        
        db.session.add(admin_user)
        db.session.commit()
        
        print("Created default admin user:")
        print("  Username: admin")
        print("  Password: admin123")
        print("  Email: <EMAIL>")
        print("  *** CHANGE THE PASSWORD IMMEDIATELY! ***")

def init_database():
    """Initialize database with default data"""
    print("Initializing database...")
    
    # Create all tables
    db.create_all()
    print("Database tables created.")
    
    # Create default roles
    create_default_roles()
    
    # Create admin user
    create_admin_user()
    
    print("Database initialization completed.")

def main():
    """Main application entry point"""
    # Get configuration from environment
    config_name = os.getenv('FLASK_ENV', 'development')

    # Create Flask app
    app = create_app(config_name)

    with app.app_context():
        # Initialize database if requested
        if len(sys.argv) > 1 and sys.argv[1] == 'init-db':
            init_database()
            return

        # Check if database exists and is initialized
        try:
            # Try to query a table to see if DB is set up
            Role.query.first()
        except Exception:
            print("Database not initialized. Run with 'init-db' argument to initialize.")
            print("Example: python app.py init-db")
            return

        # Start the application
        print(f"Starting Linux Migration Service in {config_name} mode...")
        print(f"Server will be available at: http://localhost:5000")

        if config_name == 'development':
            print("Development mode - Debug enabled")
            socketio.run(app, host='0.0.0.0', port=5000, debug=True)
        else:
            print("Production mode")
            socketio.run(app, host='0.0.0.0', port=5000)

# Create app instance for Gunicorn
app = create_app(os.getenv('FLASK_ENV', 'development'))

if __name__ == '__main__':
    main()
