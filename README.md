# Linux Migration Service

Профессиональный веб-сервис для миграции Linux серверов с современным интерфейсом, системой управления пользователями и правами доступа.

## 🚀 Возможности

- **Современный веб-интерфейс** - React + TypeScript + Material-UI
- **Система аутентификации** - JWT токены, роли и права доступа
- **Управление серверами** - Добавление, мониторинг, группировка серверов
- **Миграции** - Создание, отслеживание и управление миграциями
- **Real-time мониторинг** - WebSocket для отслеживания прогресса
- **Аудит и логирование** - Детальное логирование всех действий
- **API** - RESTful API для интеграции
- **Безопасность** - Шифрование паролей, валидация данных
- **Масштабируемость** - Docker, Redis, PostgreSQL

## 🏗️ Архитектура

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React SPA     │    │   Flask API     │    │   PostgreSQL    │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │     Redis       │
                       │  (Cache/Queue)  │
                       └─────────────────┘
```

## 📋 Требования

- Docker и Docker Compose
- 4GB RAM (минимум)
- 10GB свободного места на диске
- Порты 80, 443, 5000, 3000 (для разработки)

## 🚀 Быстрый старт

### 1. Клонирование репозитория

```bash
git clone <repository-url>
cd linux-migration-service
```

### 2. Настройка переменных окружения

```bash
# Скопируйте файл с переменными окружения
cp .env.example .env

# Отредактируйте переменные
nano .env
```

### 3. Запуск с Docker Compose

```bash
# Запуск всех сервисов
docker-compose up -d

# Просмотр логов
docker-compose logs -f

# Остановка сервисов
docker-compose down
```

### 4. Инициализация базы данных

```bash
# Выполните миграции и создайте администратора
docker-compose exec backend python app.py init-db
```

### 5. Доступ к приложению

- **Веб-интерфейс**: http://localhost
- **API**: http://localhost/api
- **Документация API**: http://localhost/api/docs

**Данные администратора по умолчанию:**
- Логин: `admin`
- Пароль: `admin123`

⚠️ **Обязательно смените пароль после первого входа!**

## 🛠️ Разработка

### Backend (Flask)

```bash
cd backend

# Создание виртуального окружения
python -m venv venv
source venv/bin/activate  # Linux/Mac
# или
venv\Scripts\activate     # Windows

# Установка зависимостей
pip install -r requirements.txt

# Запуск в режиме разработки
python app.py
```

### Frontend (React)

```bash
cd frontend

# Установка зависимостей
npm install

# Запуск в режиме разработки
npm start
```

### База данных

```bash
# Подключение к PostgreSQL
docker-compose exec postgres psql -U migration_user -d migration_service

# Создание миграции
docker-compose exec backend flask db migrate -m "Description"

# Применение миграций
docker-compose exec backend flask db upgrade
```

## 📚 API Документация

### Аутентификация

```bash
# Регистрация
POST /api/auth/register
{
  "username": "user",
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "John",
  "last_name": "Doe"
}

# Вход
POST /api/auth/login
{
  "username": "user",
  "password": "password123"
}

# Получение текущего пользователя
GET /api/auth/me
Authorization: Bearer <token>
```

### Серверы

```bash
# Список серверов
GET /api/servers?page=1&per_page=20&search=web

# Создание сервера
POST /api/servers
{
  "name": "Web Server 1",
  "hostname": "web1.example.com",
  "ip_address": "*************",
  "port": 22,
  "username": "root",
  "ssh_key_path": "/path/to/key"
}

# Тест соединения
POST /api/servers/{id}/test-connection
```

### Миграции

```bash
# Список миграций
GET /api/migrations?status=running

# Создание миграции
POST /api/migrations
{
  "name": "Web Server Migration",
  "source_server_id": "uuid",
  "target_server_id": "uuid",
  "config": {
    "migration_method": "rsync",
    "stop_services": true
  }
}

# Запуск миграции
POST /api/migrations/{id}/start

# Получение логов
GET /api/migrations/{id}/logs
```

## 🔧 Конфигурация

### Переменные окружения

```bash
# Backend
FLASK_ENV=production
DATABASE_URL=postgresql://user:pass@localhost/db
REDIS_HOST=localhost
REDIS_PORT=6379
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret

# Frontend
REACT_APP_API_URL=http://localhost:5000/api
```

### Роли и права доступа

- **admin** - Полный доступ ко всем функциям
- **operator** - Управление серверами и миграциями
- **user** - Создание миграций, просмотр серверов
- **viewer** - Только просмотр

## 🔒 Безопасность

- JWT токены с автоматическим обновлением
- Хеширование паролей с bcrypt
- Валидация всех входных данных
- Защита от CSRF и XSS атак
- Аудит всех действий пользователей
- Rate limiting для API

## 📊 Мониторинг

- Real-time обновления через WebSocket
- Детальное логирование всех операций
- Метрики производительности
- Уведомления о статусе миграций

## 🧪 Тестирование

```bash
# Backend тесты
cd backend
python -m pytest tests/

# Frontend тесты
cd frontend
npm test

# E2E тесты
npm run test:e2e
```

## 📦 Развертывание

### Production с Docker

```bash
# Сборка и запуск
docker-compose -f docker-compose.prod.yml up -d

# Обновление
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

### Без Docker

1. Установите PostgreSQL и Redis
2. Настройте веб-сервер (Nginx)
3. Запустите backend с Gunicorn
4. Соберите и разверните frontend

## 🤝 Участие в разработке

1. Fork репозитория
2. Создайте feature branch
3. Внесите изменения
4. Добавьте тесты
5. Создайте Pull Request

## 📄 Лицензия

MIT License - см. файл [LICENSE](LICENSE)

## 🆘 Поддержка

- Создайте Issue в GitHub
- Документация: [docs/](docs/)
- Email: <EMAIL>

## 📝 Changelog

### v1.0.0
- Первый релиз
- Базовая функциональность миграций
- Веб-интерфейс
- Система пользователей

---

**Linux Migration Service** - Профессиональное решение для миграции серверов 🚀
